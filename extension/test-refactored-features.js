/**
 * Chrome扩展工作区管理器重构功能测试脚本
 * 
 * 测试范围：
 * 1. 工作区切换测试（ai-main, ai-tools, daily-work, tech-forum等）
 * 2. 标签页删除功能测试（单个和批量删除）
 * 3. 固定状态测试（pin状态在工作区切换后保持一致）
 * 4. 代码质量测试（消除竞态条件，确保时序逻辑清晰）
 */

// 测试配置
const TEST_CONFIG = {
  workspaces: ['ai-main', 'ai-tools', 'daily-work', 'tech-forum'],
  testUrls: [
    'https://chat.openai.com/',
    'https://gemini.google.com/',
    'https://www.yuque.com/',
    'https://p1b9rnchwd.feishu.cn/drive/home/',
    'https://linux.do/',
    'https://deepask.cc/'
  ],
  maxWaitTime: 10000, // 10秒
  testTimeout: 30000  // 30秒
};

// 测试结果收集器
class TestResultCollector {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  addResult(testName, success, details = {}) {
    this.results.push({
      testName,
      success,
      details,
      timestamp: Date.now(),
      duration: Date.now() - this.startTime
    });
    
    console.log(`${success ? '✅' : '❌'} [TEST] ${testName}`, details);
  }

  getSummary() {
    const total = this.results.length;
    const passed = this.results.filter(r => r.success).length;
    const failed = total - passed;
    
    return {
      total,
      passed,
      failed,
      passRate: total > 0 ? (passed / total * 100).toFixed(2) : 0,
      duration: Date.now() - this.startTime,
      results: this.results
    };
  }
}

// 测试工具函数
class TestUtils {
  static async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static async waitForCondition(condition, timeout = 5000, interval = 100) {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return true;
      }
      await this.wait(interval);
    }
    return false;
  }

  static async getCurrentTabs() {
    return new Promise((resolve) => {
      chrome.tabs.query({ currentWindow: true }, resolve);
    });
  }

  static async sendMessage(type, data) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type, data }, resolve);
    });
  }

  static async switchWorkspace(workspaceId) {
    return this.sendMessage('SWITCH_WORKSPACE', { workspaceId });
  }

  static async deleteTab(tabId, workspaceId) {
    return this.sendMessage('DELETE_TAB_FROM_WORKSPACE', { tabId, workspaceId });
  }

  static async batchDeleteTabs(tabIds, workspaceId) {
    return this.sendMessage('BATCH_DELETE_TABS_FROM_WORKSPACE', { tabIds, workspaceId });
  }
}

// 主测试类
class WorkspaceManagerTests {
  constructor() {
    this.collector = new TestResultCollector();
    this.utils = TestUtils;
  }

  async runAllTests() {
    console.log('🚀 [TEST-SUITE] 开始Chrome扩展工作区管理器重构功能测试');
    console.log('🚀 [TEST-SUITE] 测试配置:', TEST_CONFIG);

    try {
      // 1. 工作区切换测试
      await this.testWorkspaceSwitching();
      
      // 2. URL去重测试
      await this.testUrlDeduplication();
      
      // 3. 标签页删除测试
      await this.testTabDeletion();
      
      // 4. 固定状态测试
      await this.testPinStates();
      
      // 5. 并发测试
      await this.testConcurrency();

    } catch (error) {
      console.error('❌ [TEST-SUITE] 测试套件执行失败:', error);
      this.collector.addResult('测试套件执行', false, { error: error.message });
    }

    // 输出测试结果
    const summary = this.collector.getSummary();
    console.log('📊 [TEST-SUITE] ========== 测试结果汇总 ==========');
    console.log(`📊 [TEST-SUITE] 总计: ${summary.total} 个测试`);
    console.log(`📊 [TEST-SUITE] 通过: ${summary.passed} 个 (${summary.passRate}%)`);
    console.log(`📊 [TEST-SUITE] 失败: ${summary.failed} 个`);
    console.log(`📊 [TEST-SUITE] 耗时: ${summary.duration}ms`);

    if (summary.failed > 0) {
      console.log('❌ [TEST-SUITE] 失败的测试:');
      summary.results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.testName}: ${result.details.error || '未知错误'}`);
      });
    }

    return summary;
  }

  // 测试1: 工作区切换功能
  async testWorkspaceSwitching() {
    console.log('🔄 [TEST-WORKSPACE] 开始工作区切换测试...');

    for (const workspaceId of TEST_CONFIG.workspaces) {
      try {
        const startTime = Date.now();
        const initialTabs = await this.utils.getCurrentTabs();
        
        // 切换到目标工作区
        const response = await this.utils.switchWorkspace(workspaceId);
        
        if (!response.success) {
          this.collector.addResult(`工作区切换-${workspaceId}`, false, { 
            error: response.error || '切换失败' 
          });
          continue;
        }

        // 等待切换完成
        await this.utils.wait(2000);
        
        const finalTabs = await this.utils.getCurrentTabs();
        const duration = Date.now() - startTime;

        // 验证切换结果
        const hasExpectedTabs = await this.verifyWorkspaceContent(workspaceId, finalTabs);
        
        this.collector.addResult(`工作区切换-${workspaceId}`, hasExpectedTabs, {
          duration,
          initialTabCount: initialTabs.length,
          finalTabCount: finalTabs.length,
          workspaceName: workspaceId
        });

      } catch (error) {
        this.collector.addResult(`工作区切换-${workspaceId}`, false, { 
          error: error.message 
        });
      }
    }
  }

  // 测试2: URL去重功能
  async testUrlDeduplication() {
    console.log('🔍 [TEST-DEDUP] 开始URL去重测试...');

    try {
      // 切换到ai-main工作区
      await this.utils.switchWorkspace('ai-main');
      await this.utils.wait(2000);

      const tabsBefore = await this.utils.getCurrentTabs();
      
      // 再次切换到同一工作区，应该不会创建重复标签页
      await this.utils.switchWorkspace('ai-main');
      await this.utils.wait(2000);

      const tabsAfter = await this.utils.getCurrentTabs();

      // 检查是否有重复的URL
      const duplicates = this.findDuplicateUrls(tabsAfter);
      const noDuplicates = duplicates.length === 0;

      this.collector.addResult('URL去重测试', noDuplicates, {
        tabsBeforeCount: tabsBefore.length,
        tabsAfterCount: tabsAfter.length,
        duplicatesFound: duplicates.length,
        duplicates: duplicates.slice(0, 3) // 只显示前3个重复项
      });

    } catch (error) {
      this.collector.addResult('URL去重测试', false, { error: error.message });
    }
  }

  // 测试3: 标签页删除功能
  async testTabDeletion() {
    console.log('🗑️ [TEST-DELETE] 开始标签页删除测试...');

    try {
      // 确保有足够的标签页进行测试
      await this.utils.switchWorkspace('ai-main');
      await this.utils.wait(2000);

      const tabs = await this.utils.getCurrentTabs();
      if (tabs.length < 2) {
        this.collector.addResult('标签页删除测试', false, { 
          error: '标签页数量不足，无法进行删除测试' 
        });
        return;
      }

      // 测试单个删除
      const tabToDelete = tabs[tabs.length - 1]; // 删除最后一个标签页
      const deleteResponse = await this.utils.deleteTab(tabToDelete.id, 'ai-main');
      
      await this.utils.wait(1000);
      const tabsAfterSingleDelete = await this.utils.getCurrentTabs();
      
      const singleDeleteSuccess = deleteResponse.success && 
        tabsAfterSingleDelete.length === tabs.length - 1;

      this.collector.addResult('单个标签页删除', singleDeleteSuccess, {
        originalCount: tabs.length,
        afterDeleteCount: tabsAfterSingleDelete.length,
        deletedTabTitle: tabToDelete.title
      });

      // 测试批量删除（如果还有足够的标签页）
      if (tabsAfterSingleDelete.length >= 2) {
        const tabsToDelete = tabsAfterSingleDelete.slice(-2).map(tab => tab.id);
        const batchDeleteResponse = await this.utils.batchDeleteTabs(tabsToDelete, 'ai-main');
        
        await this.utils.wait(1000);
        const tabsAfterBatchDelete = await this.utils.getCurrentTabs();
        
        const batchDeleteSuccess = batchDeleteResponse.success && 
          tabsAfterBatchDelete.length === tabsAfterSingleDelete.length - 2;

        this.collector.addResult('批量标签页删除', batchDeleteSuccess, {
          beforeBatchCount: tabsAfterSingleDelete.length,
          afterBatchCount: tabsAfterBatchDelete.length,
          deletedCount: tabsToDelete.length
        });
      }

    } catch (error) {
      this.collector.addResult('标签页删除测试', false, { error: error.message });
    }
  }

  // 测试4: 固定状态测试
  async testPinStates() {
    console.log('📌 [TEST-PIN] 开始固定状态测试...');

    try {
      // 切换到ai-main工作区
      await this.utils.switchWorkspace('ai-main');
      await this.utils.wait(2000);

      const tabs = await this.utils.getCurrentTabs();
      const pinnedTabs = tabs.filter(tab => tab.pinned);
      
      // 切换到其他工作区再切换回来
      await this.utils.switchWorkspace('daily-work');
      await this.utils.wait(2000);
      
      await this.utils.switchWorkspace('ai-main');
      await this.utils.wait(2000);

      const tabsAfterSwitch = await this.utils.getCurrentTabs();
      const pinnedTabsAfterSwitch = tabsAfterSwitch.filter(tab => tab.pinned);

      // 验证固定状态是否保持
      const pinStatesPreserved = this.verifyPinStatesPreserved(pinnedTabs, pinnedTabsAfterSwitch);

      this.collector.addResult('固定状态保持测试', pinStatesPreserved.success, {
        originalPinnedCount: pinnedTabs.length,
        afterSwitchPinnedCount: pinnedTabsAfterSwitch.length,
        details: pinStatesPreserved.details
      });

    } catch (error) {
      this.collector.addResult('固定状态保持测试', false, { error: error.message });
    }
  }

  // 测试5: 并发测试
  async testConcurrency() {
    console.log('⚡ [TEST-CONCURRENCY] 开始并发测试...');

    try {
      // 快速连续切换工作区
      const switchPromises = TEST_CONFIG.workspaces.slice(0, 3).map(async (workspaceId, index) => {
        await this.utils.wait(index * 500); // 错开启动时间
        return this.utils.switchWorkspace(workspaceId);
      });

      const results = await Promise.all(switchPromises);
      const allSuccessful = results.every(result => result.success);

      await this.utils.wait(3000); // 等待所有操作完成

      const finalTabs = await this.utils.getCurrentTabs();
      const duplicates = this.findDuplicateUrls(finalTabs);

      this.collector.addResult('并发切换测试', allSuccessful && duplicates.length === 0, {
        switchResults: results.map(r => r.success),
        finalTabCount: finalTabs.length,
        duplicatesFound: duplicates.length
      });

    } catch (error) {
      this.collector.addResult('并发切换测试', false, { error: error.message });
    }
  }

  // 辅助方法：验证工作区内容
  async verifyWorkspaceContent(workspaceId, tabs) {
    // 这里应该根据工作区ID验证是否包含预期的标签页
    const expectedUrls = this.getExpectedUrlsForWorkspace(workspaceId);
    const actualUrls = tabs.map(tab => tab.url).filter(Boolean);
    
    return expectedUrls.some(expectedUrl => 
      actualUrls.some(actualUrl => actualUrl.includes(expectedUrl) || expectedUrl.includes(actualUrl))
    );
  }

  // 辅助方法：获取工作区预期URL
  getExpectedUrlsForWorkspace(workspaceId) {
    const urlMap = {
      'ai-main': ['chat.openai.com', 'gemini.google.com', 'lobehub.com'],
      'ai-tools': ['deepask.cc', 'gptfun.cc', 'demo.fuclaude.com'],
      'daily-work': ['yuque.com', 'feishu.cn'],
      'tech-forum': ['linux.do', 'nodeseek.com', 'nodeloc.cc']
    };
    
    return urlMap[workspaceId] || [];
  }

  // 辅助方法：查找重复URL
  findDuplicateUrls(tabs) {
    const urlMap = new Map();
    const duplicates = [];
    
    tabs.forEach(tab => {
      if (tab.url) {
        const normalizedUrl = tab.url.split('?')[0].split('#')[0]; // 简单标准化
        if (urlMap.has(normalizedUrl)) {
          duplicates.push({
            url: normalizedUrl,
            tabs: [urlMap.get(normalizedUrl), tab]
          });
        } else {
          urlMap.set(normalizedUrl, tab);
        }
      }
    });
    
    return duplicates;
  }

  // 辅助方法：验证固定状态保持
  verifyPinStatesPreserved(originalPinned, currentPinned) {
    const originalUrls = new Set(originalPinned.map(tab => tab.url).filter(Boolean));
    const currentUrls = new Set(currentPinned.map(tab => tab.url).filter(Boolean));
    
    const preserved = [];
    const lost = [];
    
    originalUrls.forEach(url => {
      if (currentUrls.has(url)) {
        preserved.push(url);
      } else {
        lost.push(url);
      }
    });
    
    return {
      success: lost.length === 0,
      details: {
        preserved: preserved.length,
        lost: lost.length,
        lostUrls: lost.slice(0, 3) // 只显示前3个丢失的URL
      }
    };
  }
}

// 导出测试类供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { WorkspaceManagerTests, TestUtils, TestResultCollector };
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  window.WorkspaceManagerTests = WorkspaceManagerTests;
  window.TestUtils = TestUtils;
  window.TestResultCollector = TestResultCollector;
}
