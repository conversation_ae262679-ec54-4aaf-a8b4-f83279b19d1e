<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展工作区管理器 - 重构功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .test-controls {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-controls h2 {
            margin-top: 0;
            color: #333;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .test-status {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-idle { background-color: #6c757d; }
        .status-running { background-color: #ffc107; animation: pulse 1.5s infinite; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .test-results {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-results h2 {
            margin-top: 0;
            color: #333;
        }
        
        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.error { border-left-color: #dc3545; }
        .summary-card.warning { border-left-color: #ffc107; }
        
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .test-item {
            padding: 15px;
            border-left: 4px solid #ddd;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 0 6px 6px 0;
        }
        
        .test-item.success { border-left-color: #28a745; }
        .test-item.error { border-left-color: #dc3545; }
        
        .test-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .test-details {
            font-size: 0.9em;
            color: #666;
        }
        
        .log-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .hidden {
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Chrome扩展工作区管理器</h1>
        <p>重构功能测试套件 - 验证核心功能的正确性和稳定性</p>
    </div>

    <div class="test-controls">
        <h2>测试控制</h2>
        <div class="button-group">
            <button id="runAllTests" class="btn-primary">🚀 运行全部测试</button>
            <button id="runWorkspaceTests" class="btn-secondary">🔄 工作区切换测试</button>
            <button id="runDedupTests" class="btn-secondary">🔍 URL去重测试</button>
            <button id="runDeleteTests" class="btn-secondary">🗑️ 删除功能测试</button>
            <button id="runPinTests" class="btn-secondary">📌 固定状态测试</button>
            <button id="clearResults" class="btn-danger">🗑️ 清除结果</button>
        </div>
    </div>

    <div class="test-status">
        <h2>测试状态</h2>
        <div id="statusIndicator">
            <span class="status-indicator status-idle"></span>
            <span id="statusText">就绪 - 点击按钮开始测试</span>
        </div>
        <div class="progress-bar">
            <div id="progressFill" class="progress-fill"></div>
        </div>
        <div id="currentTest" class="hidden">
            <strong>当前测试:</strong> <span id="currentTestName">-</span>
        </div>
    </div>

    <div class="test-results">
        <h2>测试结果</h2>
        <div id="resultsSummary" class="results-summary hidden">
            <div class="summary-card">
                <div id="totalTests" class="summary-number">0</div>
                <div class="summary-label">总测试数</div>
            </div>
            <div class="summary-card success">
                <div id="passedTests" class="summary-number">0</div>
                <div class="summary-label">通过</div>
            </div>
            <div class="summary-card error">
                <div id="failedTests" class="summary-number">0</div>
                <div class="summary-label">失败</div>
            </div>
            <div class="summary-card">
                <div id="passRate" class="summary-number">0%</div>
                <div class="summary-label">通过率</div>
            </div>
        </div>
        
        <div id="testDetails" class="hidden">
            <h3>详细结果</h3>
            <div id="testList"></div>
        </div>
        
        <div id="logOutput" class="log-output hidden"></div>
    </div>

    <script src="test-refactored-features.js"></script>
    <script>
        class TestRunner {
            constructor() {
                this.testManager = null;
                this.isRunning = false;
                this.initializeUI();
            }

            initializeUI() {
                // 绑定按钮事件
                document.getElementById('runAllTests').addEventListener('click', () => this.runAllTests());
                document.getElementById('runWorkspaceTests').addEventListener('click', () => this.runSpecificTest('workspace'));
                document.getElementById('runDedupTests').addEventListener('click', () => this.runSpecificTest('dedup'));
                document.getElementById('runDeleteTests').addEventListener('click', () => this.runSpecificTest('delete'));
                document.getElementById('runPinTests').addEventListener('click', () => this.runSpecificTest('pin'));
                document.getElementById('clearResults').addEventListener('click', () => this.clearResults());
            }

            async runAllTests() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateStatus('running', '正在运行全部测试...');
                this.clearResults();

                try {
                    this.testManager = new WorkspaceManagerTests();
                    const results = await this.testManager.runAllTests();
                    this.displayResults(results);
                    this.updateStatus('success', '所有测试完成');
                } catch (error) {
                    console.error('测试运行失败:', error);
                    this.updateStatus('error', `测试失败: ${error.message}`);
                } finally {
                    this.isRunning = false;
                }
            }

            async runSpecificTest(testType) {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateStatus('running', `正在运行${testType}测试...`);
                this.clearResults();

                try {
                    this.testManager = new WorkspaceManagerTests();
                    
                    switch (testType) {
                        case 'workspace':
                            await this.testManager.testWorkspaceSwitching();
                            break;
                        case 'dedup':
                            await this.testManager.testUrlDeduplication();
                            break;
                        case 'delete':
                            await this.testManager.testTabDeletion();
                            break;
                        case 'pin':
                            await this.testManager.testPinStates();
                            break;
                    }
                    
                    const results = this.testManager.collector.getSummary();
                    this.displayResults(results);
                    this.updateStatus('success', `${testType}测试完成`);
                } catch (error) {
                    console.error('测试运行失败:', error);
                    this.updateStatus('error', `测试失败: ${error.message}`);
                } finally {
                    this.isRunning = false;
                }
            }

            updateStatus(status, text) {
                const indicator = document.querySelector('.status-indicator');
                const statusText = document.getElementById('statusText');
                
                indicator.className = `status-indicator status-${status}`;
                statusText.textContent = text;
            }

            displayResults(results) {
                // 显示汇总
                document.getElementById('totalTests').textContent = results.total;
                document.getElementById('passedTests').textContent = results.passed;
                document.getElementById('failedTests').textContent = results.failed;
                document.getElementById('passRate').textContent = `${results.passRate}%`;
                
                document.getElementById('resultsSummary').classList.remove('hidden');
                
                // 显示详细结果
                const testList = document.getElementById('testList');
                testList.innerHTML = '';
                
                results.results.forEach(result => {
                    const testItem = document.createElement('div');
                    testItem.className = `test-item ${result.success ? 'success' : 'error'}`;
                    
                    testItem.innerHTML = `
                        <div class="test-name">${result.success ? '✅' : '❌'} ${result.testName}</div>
                        <div class="test-details">
                            耗时: ${result.duration}ms | 
                            ${result.success ? '通过' : `失败: ${result.details.error || '未知错误'}`}
                            ${result.details && Object.keys(result.details).length > 1 ? 
                                `<br>详情: ${JSON.stringify(result.details, null, 2)}` : ''}
                        </div>
                    `;
                    
                    testList.appendChild(testItem);
                });
                
                document.getElementById('testDetails').classList.remove('hidden');
            }

            clearResults() {
                document.getElementById('resultsSummary').classList.add('hidden');
                document.getElementById('testDetails').classList.add('hidden');
                document.getElementById('logOutput').classList.add('hidden');
                document.getElementById('progressFill').style.width = '0%';
            }
        }

        // 初始化测试运行器
        document.addEventListener('DOMContentLoaded', () => {
            new TestRunner();
        });
    </script>
</body>
</html>
