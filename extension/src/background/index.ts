// Chrome Extension Background Script (Service Worker)
// 工作区管理器 - 专注于工作区管理功能

// 类型定义
interface Workspace {
  id: string;
  name: string;
  icon: string;
  color: string;
  groups: TabGroup[];
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface TabGroup {
  id: string;
  name: string;
  color: string;
  tabs: TabInfo[];
}

interface TabInfo {
  id: number;
  title: string;
  url: string;
  favIconUrl?: string;
  groupId?: number;
  pinned?: boolean;
}

// 全局状态
let currentWorkspaceId: string = 'default';
let workspaces = new Map<string, Workspace>();

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('工作区管理器扩展已安装', details);

  // 初始化工作区管理器
  await initializeWorkspaceManager();

  if (details.reason === 'install') {
    // 首次安装时创建默认工作空间
    await initializeDefaultWorkspaces();

    console.log('扩展安装完成，请点击扩展图标打开工作区管理器');
  }
});

// 初始化工作区管理器
async function initializeWorkspaceManager() {
  try {
    console.log('初始化工作区管理器...');

    // 加载存储的工作区数据
    await loadWorkspacesFromStorage();

    // 设置标签页监听器
    setupTabListeners();

    // 设置侧边栏
    await setupSidePanel();

    console.log('工作区管理器初始化成功');
  } catch (error) {
    console.error('工作区管理器初始化失败:', error);
  }
}

// 加载工作区数据
async function loadWorkspacesFromStorage() {
  try {
    const result = await chrome.storage.local.get(['workspaces', 'currentWorkspaceId']);

    if (result.workspaces) {
      workspaces = new Map(Object.entries(result.workspaces));
    }

    if (result.currentWorkspaceId) {
      currentWorkspaceId = result.currentWorkspaceId;
    }

    // 如果没有工作区，创建默认工作区
    if (workspaces.size === 0) {
      console.log('没有找到工作区，创建默认工作区...');
      await initializeDefaultWorkspaces();
    }

    console.log('已加载工作区数据:', workspaces.size, '个工作区');
  } catch (error) {
    console.error('加载工作区数据失败:', error);
  }
}

// 初始化默认工作空间
async function initializeDefaultWorkspaces() {
  try {
    // AI工作主力
    const aiMainWorkspace: Workspace = {
      id: 'ai-main',
      name: 'AI工作主力',
      color: '#8b5cf6',
      icon: '🤖',
      groups: [{
        id: 'ai-main-group',
        name: 'AI主力工具',
        color: 'purple',
        tabs: [
          { id: 0, title: 'ChatGPT', url: 'https://chat.openai.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Gemini', url: 'https://gemini.google.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'LobeHub', url: 'https://chat-preview.lobehub.com/discover', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Perplexity', url: 'https://www.perplexity.ai/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Grok', url: 'https://grok.x.ai/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'AI Studio', url: 'https://aistudio.google.com/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // AI次选
    const aiSecondaryWorkspace: Workspace = {
      id: 'ai-secondary',
      name: 'AI次选',
      color: '#06b6d4',
      icon: '🔧',
      groups: [{
        id: 'ai-secondary-group',
        name: 'AI次选工具',
        color: 'cyan',
        tabs: [
          { id: 0, title: 'DeepAsk', url: 'https://deepask.cc/#/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'GPTFun', url: 'https://fun4ai.khthink.cn/login', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'C佬', url: 'https://new.clivia.fun/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'A佬', url: 'https://aabao.eu.cc/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'H佬', url: 'https://work.haomo.de/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Claude', url: 'https://demo.fuclaude.com/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // AI其他工具
    const aiToolsWorkspace: Workspace = {
      id: 'ai-tools',
      name: 'AI其他工具',
      color: '#f59e0b',
      icon: '⚡',
      groups: [{
        id: 'ai-tools-group',
        name: 'AI辅助工具',
        color: 'yellow',
        tabs: [
          { id: 0, title: 'Dify', url: 'https://dify.ai/', favIconUrl: '', groupId: 0 },
          { id: 0, title: '提示词优化', url: 'https://promptpilot.volcengine.com/home', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 技术论坛
    const techForumWorkspace: Workspace = {
      id: 'tech-forum',
      name: '技术论坛',
      color: '#10b981',
      icon: '💬',
      groups: [{
        id: 'tech-forum-group',
        name: '技术社区',
        color: 'green',
        tabs: [
          { id: 0, title: 'Linux.do', url: 'https://linux.do/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'NodeLoc', url: 'https://nodeloc.cc/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'NodeSeek', url: 'https://www.nodeseek.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Appinn', url: 'https://meta.appinn.net/latest', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Follow', url: 'https://app.follow.is/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 日常工作
    const dailyWorkWorkspace: Workspace = {
      id: 'daily-work',
      name: '日常工作',
      color: '#ef4444',
      icon: '💼',
      groups: [{
        id: 'daily-work-group',
        name: '工作平台',
        color: 'red',
        tabs: [
          { id: 0, title: '语雀', url: 'https://www.yuque.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: '飞书', url: 'https://p1b9rnchwd.feishu.cn/drive/home/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 创建默认工作区
    const defaultWorkspace: Workspace = {
      id: 'default',
      name: '默认工作区',
      color: '#3b82f6',
      icon: '🏠',
      groups: [],
      isDefault: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 添加所有工作区
    workspaces.set('default', defaultWorkspace);
    workspaces.set('ai-main', aiMainWorkspace);
    workspaces.set('ai-secondary', aiSecondaryWorkspace);
    workspaces.set('ai-tools', aiToolsWorkspace);
    workspaces.set('tech-forum', techForumWorkspace);
    workspaces.set('daily-work', dailyWorkWorkspace);

    // 设置默认当前工作区
    currentWorkspaceId = 'ai-main';

    await saveWorkspacesToStorage();
    console.log('默认工作区已创建:', workspaces.size, '个工作区');
  } catch (error) {
    console.error('创建默认工作区失败:', error);
  }
}

// 设置标签页监听器
function setupTabListeners() {
  // 监听标签页创建
  chrome.tabs.onCreated.addListener(async (tab) => {
    await handleTabCreated(tab);
  });

  // 监听标签页更新
  chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
      await handleTabUpdated(tab);
    }
  });

  // 监听标签页移除
  chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
    await handleTabRemoved(tabId);
  });

  // 标签页分组功能已移除，改用固定功能
}

// 设置侧边栏
async function setupSidePanel() {
  try {
    // 设置侧边栏默认路径
    await chrome.sidePanel.setOptions({
      path: 'sidepanel.html',
      enabled: true
    });

    // 监听action点击，打开侧边栏
    chrome.action.onClicked.addListener(async (tab) => {
      await chrome.sidePanel.open({ windowId: tab.windowId });
    });
  } catch (error) {
    console.error('设置侧边栏失败:', error);
  }
}

// 打开侧边栏
async function openSidePanel() {
  try {
    const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (currentTab?.windowId) {
      await chrome.sidePanel.open({ windowId: currentTab.windowId });
    }
  } catch (error) {
    console.error('打开侧边栏失败:', error);
  }
}

// 标签页事件处理函数
async function handleTabCreated(tab: chrome.tabs.Tab) {
  console.log('标签页创建:', tab.title, tab.url);
  // 可以在这里添加自动分组逻辑
}

async function handleTabUpdated(tab: chrome.tabs.Tab) {
  console.log('标签页更新:', tab.title, tab.url);
  // 可以在这里添加自动分类逻辑
}

async function handleTabRemoved(tabId: number) {
  console.log('标签页移除:', tabId);
  // 从工作区中移除对应的标签页记录
}

// 标签页分组处理函数已移除，改用固定功能

// 保存工作区数据到存储
async function saveWorkspacesToStorage() {
  try {
    const workspacesObj = Object.fromEntries(workspaces);
    await chrome.storage.local.set({
      workspaces: workspacesObj,
      currentWorkspaceId: currentWorkspaceId
    });
  } catch (error) {
    console.error('保存工作区数据失败:', error);
  }
}

// 监听快捷键命令
chrome.commands.onCommand.addListener(async (command) => {
  console.log('快捷键命令:', command);

  switch (command) {
    case 'toggle-workspace':
      await handleToggleWorkspace();
      break;
    case 'open-workspace-manager':
      // 快捷键触发也算用户手势，可以打开侧边栏
      await openSidePanel();
      break;
  }
});

// 切换工作空间
async function handleToggleWorkspace() {
  try {
    // 获取所有工作区，切换到下一个
    const workspaceIds = Array.from(workspaces.keys());
    const currentIndex = workspaceIds.indexOf(currentWorkspaceId);
    const nextIndex = (currentIndex + 1) % workspaceIds.length;
    const nextWorkspaceId = workspaceIds[nextIndex];

    await switchToWorkspace(nextWorkspaceId);
  } catch (error) {
    console.error('切换工作空间失败:', error);
  }
}

// 监听来自sidepanel的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message);

  switch (message.action) {
    case 'GET_CURRENT_TABS':
      handleGetCurrentTabs(sendResponse);
      return true; // 保持消息通道开放

    case 'GET_CURRENT_WORKSPACE':
      handleGetCurrentWorkspace(sendResponse);
      return true;

    case 'GET_WORKSPACES':
      handleGetWorkspaces(sendResponse);
      return true;

    case 'GET_WORKSPACE':
      handleGetWorkspace(message.workspaceId, sendResponse);
      return true;

    case 'SWITCH_WORKSPACE':
      handleSwitchWorkspace(message.workspaceId, sendResponse);
      return true;

    case 'CREATE_WORKSPACE':
      handleCreateWorkspace(message, sendResponse);
      return true;

    case 'AI_AUTO_GROUP_PREVIEW':
      handleAIAutoGroupPreview(message.workspaceId, sendResponse);
      return true;

    case 'AI_AUTO_GROUP_APPLY':
      handleAIAutoGroupApply(message, sendResponse);
      return true;

    case 'ADD_TAB_TO_WORKSPACE':
      handleAddTabToWorkspace(message, sendResponse);
      return true;

    case 'AI_ANALYZE_URL':
      handleAIAnalyzeURL(message, sendResponse);
      return true;

    case 'HEALTH_CHECK':
      handleHealthCheck(sendResponse);
      return true;

    case 'DELETE_WORKSPACE':
      console.log('🗑️ 处理删除工作区请求:', message);
      handleDeleteWorkspace(message.workspaceId, sendResponse);
      return true;

    case 'DELETE_TAB_FROM_WORKSPACE':
      console.log('🗑️ 处理删除标签页请求:', message);
      handleDeleteTabFromWorkspace(message, sendResponse);
      return true;

    case 'BATCH_DELETE_TABS_FROM_WORKSPACE':
      handleBatchDeleteTabsFromWorkspace(message, sendResponse);
      return true;

    case 'VERIFY_PINNING':
      handleVerifyPinning(message.workspaceId, sendResponse);
      return true;

    case 'FORCE_APPLY_PINNING':
      handleForceApplyPinning(message.workspaceId, sendResponse);
      return true;

    case 'SYNC_WORKSPACE_TO_CLOUD':
      handleSyncWorkspaceToCloud(message.workspaceId, sendResponse);
      return true;

    case 'RESTORE_WORKSPACE_FROM_CLOUD':
      handleRestoreWorkspaceFromCloud(message.workspaceId, sendResponse);
      return true;

    case 'GET_SYNC_STATUS':
      handleGetSyncStatus(sendResponse);
      return true;

    case 'ENABLE_CLOUD_SYNC':
      handleEnableCloudSync(message.enabled, sendResponse);
      return true;
  }
});

// 消息处理函数实现

// 健康检查处理
async function handleHealthCheck(sendResponse: (response: any) => void) {
  try {
    sendResponse({
      success: true,
      timestamp: Date.now(),
      workspaceId: currentWorkspaceId,
      status: 'healthy'
    });
  } catch (error: any) {
    console.error('健康检查失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 删除工作区处理（增强版本，添加详细日志）
async function handleDeleteWorkspace(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    console.log(`🗑️ [DEBUG-DELETE] ========== 开始删除工作区 ==========`);
    console.log(`🗑️ [DEBUG-DELETE] 工作区ID: ${workspaceId}`);

    if (!workspaceId) {
      console.error('❌ [DEBUG-DELETE] 工作区ID为空');
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    // 检查工作区是否存在
    console.log(`🔍 [DEBUG-DELETE] 检查工作区是否存在...`);
    console.log(`🔍 [DEBUG-DELETE] 当前工作区总数: ${workspaces.size}`);
    console.log(`🔍 [DEBUG-DELETE] 所有工作区ID:`, Array.from(workspaces.keys()));

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      console.error(`❌ [DEBUG-DELETE] 工作区不存在: ${workspaceId}`);
      console.error(`❌ [DEBUG-DELETE] 可用的工作区:`, Array.from(workspaces.entries()).map(([id, ws]) => ({ id, name: ws.name })));
      sendResponse({ success: false, error: '工作区不存在' });
      return;
    }

    console.log(`📋 [DEBUG-DELETE] 找到工作区: ${workspace.name}, 是否为默认: ${workspace.isDefault}`);

    // 检查是否是默认工作区
    if (workspace.isDefault) {
      console.error('❌ [DEBUG-DELETE] 尝试删除默认工作区');
      sendResponse({ success: false, error: '不能删除默认工作区' });
      return;
    }

    // 删除工作区
    console.log(`🗑️ [DEBUG-DELETE] 执行删除操作...`);
    const deleted = workspaces.delete(workspaceId);
    console.log(`🗑️ [DEBUG-DELETE] 删除操作结果: ${deleted}`);
    console.log(`🗑️ [DEBUG-DELETE] 删除后工作区总数: ${workspaces.size}`);

    if (deleted) {
      console.log(`💾 [DEBUG-DELETE] 保存工作区数据到存储...`);
      await saveWorkspacesToStorage();
      console.log(`💾 [DEBUG-DELETE] 工作区数据已保存`);

      // 如果删除的是当前工作区，切换到默认工作区
      if (currentWorkspaceId === workspaceId) {
        console.log(`🔄 [DEBUG-DELETE] 删除的是当前工作区，需要切换到默认工作区`);
        console.log(`🔄 [DEBUG-DELETE] 原当前工作区ID: ${currentWorkspaceId}`);

        const defaultWorkspace = Array.from(workspaces.values()).find(ws => ws.isDefault);
        if (defaultWorkspace) {
          currentWorkspaceId = defaultWorkspace.id;
          await saveWorkspacesToStorage();
          console.log(`✅ [DEBUG-DELETE] 已切换到默认工作区: ${defaultWorkspace.name} (ID: ${defaultWorkspace.id})`);
        } else {
          console.warn(`⚠️ [DEBUG-DELETE] 未找到默认工作区，重置当前工作区ID`);
          currentWorkspaceId = '';
        }
      }

      console.log(`✅ [DEBUG-DELETE] 工作区删除成功`);
      sendResponse({ success: true, message: '工作区已删除' });
    } else {
      console.error(`❌ [DEBUG-DELETE] 删除操作失败`);
      sendResponse({ success: false, error: '删除操作失败' });
    }
  } catch (error: any) {
    console.error(`❌ [DEBUG-DELETE] 删除工作区异常:`, error);
    console.error(`❌ [DEBUG-DELETE] 错误堆栈:`, error.stack);
    sendResponse({ success: false, error: error.message });
  }
}

// ========== 重构的标签页删除功能 ==========

/**
 * 增强的单个标签页删除功能
 */
async function handleDeleteTabFromWorkspace(data: any, sendResponse: (response: any) => void) {
  const startTime = Date.now();

  try {
    console.log('🗑️ [DELETE-TAB] ========== 开始删除标签页 ==========');
    console.log('🗑️ [DELETE-TAB] 请求数据:', data);

    const { tabId, workspaceId } = data;

    // 参数验证
    if (!tabId || !workspaceId) {
      console.error('❌ [DELETE-TAB] 参数不完整:', { tabId, workspaceId });
      sendResponse({
        success: false,
        error: '参数不完整',
        details: { tabId: !!tabId, workspaceId: !!workspaceId }
      });
      return;
    }

    const tabIdInt = parseInt(tabId);
    if (isNaN(tabIdInt) || tabIdInt <= 0) {
      console.error('❌ [DELETE-TAB] 无效的标签页ID:', tabId);
      sendResponse({ success: false, error: '无效的标签页ID' });
      return;
    }

    console.log(`🗑️ [DELETE-TAB] 目标标签页ID: ${tabIdInt}`);

    // 步骤1: 获取标签页信息（用于日志和验证）
    let tabInfo = null;
    try {
      tabInfo = await chrome.tabs.get(tabIdInt);
      console.log(`📋 [DELETE-TAB] 标签页信息: ${tabInfo.title} - ${tabInfo.url}`);
    } catch (error) {
      console.warn(`⚠️ [DELETE-TAB] 无法获取标签页信息 (可能已关闭): ${tabIdInt}`);
    }

    // 步骤2: 删除真实的浏览器标签页
    console.log('🗑️ [DELETE-TAB] 步骤2: 删除浏览器标签页...');
    try {
      await chrome.tabs.remove(tabIdInt);
      console.log(`✅ [DELETE-TAB] 成功删除浏览器标签页 ID: ${tabIdInt}`);
    } catch (error) {
      console.error(`❌ [DELETE-TAB] 删除浏览器标签页失败 ID: ${tabIdInt}`, error);
      sendResponse({
        success: false,
        error: '删除浏览器标签页失败',
        details: (error as Error).message
      });
      return;
    }

    // 步骤3: 从工作区配置中移除
    console.log('🗑️ [DELETE-TAB] 步骤3: 从工作区配置中移除...');
    const configResult = await removeTabFromWorkspaceConfig(tabIdInt, workspaceId);

    // 步骤4: 通知其他组件标签页已删除
    console.log('🗑️ [DELETE-TAB] 步骤4: 广播删除事件...');
    await broadcastTabDeletionEvent(tabIdInt, workspaceId, tabInfo);

    const duration = Date.now() - startTime;
    console.log(`🎉 [DELETE-TAB] ========== 删除完成 (${duration}ms) ==========`);

    sendResponse({
      success: true,
      message: '标签页已删除',
      deletedTab: {
        id: tabIdInt,
        title: tabInfo?.title || '未知',
        url: tabInfo?.url || '未知'
      },
      configUpdated: configResult.updated,
      duration
    });

  } catch (error: any) {
    console.error('❌ [DELETE-TAB] 删除标签页异常:', error);
    sendResponse({
      success: false,
      error: '删除操作异常',
      details: error.message
    });
  }
}

/**
 * 增强的批量标签页删除功能
 */
async function handleBatchDeleteTabsFromWorkspace(data: any, sendResponse: (response: any) => void) {
  const startTime = Date.now();

  try {
    console.log('🗑️ [BATCH-DELETE] ========== 开始批量删除标签页 ==========');
    console.log('🗑️ [BATCH-DELETE] 请求数据:', data);

    const { tabIds, workspaceId } = data;

    // 参数验证
    if (!tabIds || !Array.isArray(tabIds) || tabIds.length === 0 || !workspaceId) {
      console.error('❌ [BATCH-DELETE] 参数不完整:', { tabIds, workspaceId });
      sendResponse({
        success: false,
        error: '参数不完整',
        details: {
          hasTabIds: !!tabIds,
          isArray: Array.isArray(tabIds),
          tabCount: tabIds?.length || 0,
          hasWorkspaceId: !!workspaceId
        }
      });
      return;
    }

    const tabIdsToDelete = tabIds.map(id => parseInt(id)).filter(id => !isNaN(id) && id > 0);
    if (tabIdsToDelete.length === 0) {
      console.error('❌ [BATCH-DELETE] 没有有效的标签页ID');
      sendResponse({ success: false, error: '没有有效的标签页ID' });
      return;
    }

    console.log(`🗑️ [BATCH-DELETE] 要删除的标签页ID: [${tabIdsToDelete.join(', ')}]`);

    // 步骤1: 收集标签页信息（用于日志和反馈）
    console.log('📋 [BATCH-DELETE] 步骤1: 收集标签页信息...');
    const tabInfos = await collectTabInfos(tabIdsToDelete);

    // 步骤2: 批量删除浏览器标签页
    console.log('🗑️ [BATCH-DELETE] 步骤2: 批量删除浏览器标签页...');
    const deleteResults = await batchDeleteBrowserTabs(tabIdsToDelete);

    // 步骤3: 从工作区配置中批量移除
    console.log('🗑️ [BATCH-DELETE] 步骤3: 从工作区配置中批量移除...');
    const configResult = await batchRemoveTabsFromWorkspaceConfig(deleteResults.successful, workspaceId);

    // 步骤4: 广播批量删除事件
    console.log('🗑️ [BATCH-DELETE] 步骤4: 广播批量删除事件...');
    await broadcastBatchDeletionEvent(deleteResults.successful, workspaceId, tabInfos);

    const duration = Date.now() - startTime;
    console.log(`🎉 [BATCH-DELETE] ========== 批量删除完成 (${duration}ms) ==========`);
    console.log(`🎉 [BATCH-DELETE] 成功: ${deleteResults.successful.length}, 失败: ${deleteResults.failed.length}`);

    if (deleteResults.successful.length > 0) {
      const message = deleteResults.failed.length > 0
        ? `已删除 ${deleteResults.successful.length} 个标签页，${deleteResults.failed.length} 个失败`
        : `已删除 ${deleteResults.successful.length} 个标签页`;

      sendResponse({
        success: true,
        message,
        deletedCount: deleteResults.successful.length,
        failedCount: deleteResults.failed.length,
        deletedTabs: deleteResults.successful.map(id => tabInfos.get(id)).filter(Boolean),
        failedTabs: deleteResults.failed,
        configUpdated: configResult.updated,
        duration
      });
    } else {
      sendResponse({
        success: false,
        error: '没有成功删除任何标签页',
        failedTabs: deleteResults.failed
      });
    }

  } catch (error: any) {
    console.error('❌ [BATCH-DELETE] 批量删除标签页异常:', error);
    sendResponse({
      success: false,
      error: '批量删除操作异常',
      details: error.message
    });
  }
}

/**
 * 从工作区配置中移除标签页
 */
async function removeTabFromWorkspaceConfig(tabId: number, workspaceId: string): Promise<{
  updated: boolean;
  removedFrom?: string;
}> {
  try {
    const workspace = workspaces.get(workspaceId);
    if (!workspace || !workspace.groups) {
      console.log(`📋 [CONFIG-REMOVE] 工作区 ${workspaceId} 不存在或无分组`);
      return { updated: false };
    }

    console.log(`📋 [CONFIG-REMOVE] 在工作区 ${workspace.name} 中查找标签页 ${tabId}`);

    for (const group of workspace.groups) {
      const tabIndex = group.tabs.findIndex(tab => tab.id === tabId);
      if (tabIndex !== -1) {
        const removedTab = group.tabs[tabIndex];
        group.tabs.splice(tabIndex, 1);

        workspace.updatedAt = new Date().toISOString();
        workspaces.set(workspaceId, workspace);
        await saveWorkspacesToStorage();

        console.log(`✅ [CONFIG-REMOVE] 从分组 ${group.name} 移除标签页: ${removedTab.title}`);
        return { updated: true, removedFrom: group.name };
      }
    }

    console.log(`📋 [CONFIG-REMOVE] 标签页 ${tabId} 不在工作区配置中`);
    return { updated: false };

  } catch (error) {
    console.error('❌ [CONFIG-REMOVE] 从工作区配置移除标签页失败:', error);
    return { updated: false };
  }
}

/**
 * 收集标签页信息
 */
async function collectTabInfos(tabIds: number[]): Promise<Map<number, {
  id: number;
  title: string;
  url: string;
  pinned: boolean;
}>> {
  const tabInfos = new Map();

  for (const tabId of tabIds) {
    try {
      const tab = await chrome.tabs.get(tabId);
      tabInfos.set(tabId, {
        id: tabId,
        title: tab.title || '未知',
        url: tab.url || '未知',
        pinned: tab.pinned || false
      });
    } catch (error) {
      console.warn(`⚠️ [COLLECT-INFO] 无法获取标签页 ${tabId} 的信息:`, error);
      tabInfos.set(tabId, {
        id: tabId,
        title: '未知',
        url: '未知',
        pinned: false
      });
    }
  }

  return tabInfos;
}

/**
 * 批量删除浏览器标签页
 */
async function batchDeleteBrowserTabs(tabIds: number[]): Promise<{
  successful: number[];
  failed: Array<{ id: number; error: string }>;
}> {
  const successful: number[] = [];
  const failed: Array<{ id: number; error: string }> = [];

  // 使用并发删除，但限制并发数量以避免浏览器过载
  const batchSize = 5;
  const batches = [];

  for (let i = 0; i < tabIds.length; i += batchSize) {
    batches.push(tabIds.slice(i, i + batchSize));
  }

  for (const batch of batches) {
    const batchPromises = batch.map(async (tabId) => {
      try {
        await chrome.tabs.remove(tabId);
        successful.push(tabId);
        console.log(`✅ [BATCH-DELETE-BROWSER] 成功删除标签页 ${tabId}`);
      } catch (error) {
        failed.push({ id: tabId, error: (error as Error).message });
        console.error(`❌ [BATCH-DELETE-BROWSER] 删除标签页 ${tabId} 失败:`, error);
      }
    });

    await Promise.all(batchPromises);

    // 批次间短暂延迟
    if (batches.indexOf(batch) < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return { successful, failed };
}

/**
 * 从工作区配置中批量移除标签页
 */
async function batchRemoveTabsFromWorkspaceConfig(tabIds: number[], workspaceId: string): Promise<{
  updated: boolean;
  removedCount: number;
}> {
  try {
    const workspace = workspaces.get(workspaceId);
    if (!workspace || !workspace.groups) {
      return { updated: false, removedCount: 0 };
    }

    let totalRemoved = 0;

    for (const group of workspace.groups) {
      const originalLength = group.tabs.length;
      group.tabs = group.tabs.filter(tab => !tabIds.includes(tab.id || 0));
      const removedFromGroup = originalLength - group.tabs.length;
      totalRemoved += removedFromGroup;

      if (removedFromGroup > 0) {
        console.log(`🗑️ [BATCH-CONFIG-REMOVE] 从分组 ${group.name} 移除了 ${removedFromGroup} 个标签页`);
      }
    }

    if (totalRemoved > 0) {
      workspace.updatedAt = new Date().toISOString();
      workspaces.set(workspaceId, workspace);
      await saveWorkspacesToStorage();
      console.log(`✅ [BATCH-CONFIG-REMOVE] 总计从工作区配置移除 ${totalRemoved} 个标签页`);
    }

    return { updated: totalRemoved > 0, removedCount: totalRemoved };

  } catch (error) {
    console.error('❌ [BATCH-CONFIG-REMOVE] 批量从工作区配置移除标签页失败:', error);
    return { updated: false, removedCount: 0 };
  }
}

/**
 * 广播标签页删除事件
 */
async function broadcastTabDeletionEvent(tabId: number, workspaceId: string, tabInfo: any) {
  try {
    const event = {
      type: 'TAB_DELETED',
      data: {
        tabId,
        workspaceId,
        tabInfo,
        timestamp: new Date().toISOString()
      }
    };

    // 发送到所有活动的扩展页面
    chrome.runtime.sendMessage(event).catch(() => {
      // 忽略没有监听器的错误
    });

    console.log(`📡 [EVENT-BROADCAST] 已广播标签页删除事件: ${tabId}`);
  } catch (error) {
    console.warn('⚠️ [EVENT-BROADCAST] 广播标签页删除事件失败:', error);
  }
}

/**
 * 广播批量删除事件
 */
async function broadcastBatchDeletionEvent(tabIds: number[], workspaceId: string, tabInfos: Map<number, any>) {
  try {
    const event = {
      type: 'BATCH_TABS_DELETED',
      data: {
        tabIds,
        workspaceId,
        tabInfos: Array.from(tabInfos.entries()),
        timestamp: new Date().toISOString()
      }
    };

    // 发送到所有活动的扩展页面
    chrome.runtime.sendMessage(event).catch(() => {
      // 忽略没有监听器的错误
    });

    console.log(`📡 [EVENT-BROADCAST] 已广播批量删除事件: [${tabIds.join(', ')}]`);
  } catch (error) {
    console.warn('⚠️ [EVENT-BROADCAST] 广播批量删除事件失败:', error);
  }
}

// 验证固定状态处理
async function handleVerifyPinning(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      sendResponse({ success: false, error: '工作区不存在' });
      return;
    }

    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);
    const allTabs = await chrome.tabs.query({ currentWindow: true });

    const verificationResults = [];
    let correctCount = 0;
    let incorrectCount = 0;

    for (const tab of allTabs) {
      if (!tab.id || !tab.url) continue;

      const shouldBePinned = pinnedUrls.some(pattern => tab.url!.includes(pattern));
      const currentlyPinned = tab.pinned || false;
      const isCorrect = shouldBePinned === currentlyPinned;

      if (isCorrect) {
        correctCount++;
      } else {
        incorrectCount++;
      }

      verificationResults.push({
        tabId: tab.id,
        title: tab.title,
        url: tab.url,
        shouldBePinned,
        currentlyPinned,
        isCorrect
      });
    }

    sendResponse({
      success: true,
      results: verificationResults,
      summary: {
        total: allTabs.length,
        correct: correctCount,
        incorrect: incorrectCount,
        accuracy: allTabs.length > 0 ? (correctCount / allTabs.length * 100).toFixed(1) : '0'
      }
    });
  } catch (error: any) {
    console.error('验证固定状态失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 强制应用固定设置处理
async function handleForceApplyPinning(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      sendResponse({ success: false, error: '工作区不存在' });
      return;
    }

    console.log(`强制应用工作区 ${workspaceId} 的固定设置...`);
    await applyWorkspacePinning(workspace);

    sendResponse({
      success: true,
      message: '固定设置已强制应用',
      workspaceId
    });
  } catch (error: any) {
    console.error('强制应用固定设置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 同步工作区到云端处理
async function handleSyncWorkspaceToCloud(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    await syncWorkspaceToCloud(workspaceId);
    sendResponse({ success: true, message: '工作区已同步到云端' });
  } catch (error: any) {
    console.error('同步工作区到云端失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 从云端恢复工作区处理
async function handleRestoreWorkspaceFromCloud(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    const result = await chrome.storage.sync.get([`cloud_workspace_${workspaceId}`]);
    const cloudData = result[`cloud_workspace_${workspaceId}`];

    if (!cloudData) {
      sendResponse({ success: false, error: '云端没有找到该工作区数据' });
      return;
    }

    // 恢复工作区数据
    workspaces.set(workspaceId, cloudData.workspace);
    await saveWorkspacesToStorage();

    sendResponse({
      success: true,
      message: '工作区已从云端恢复',
      workspace: cloudData.workspace,
      timestamp: cloudData.timestamp
    });
  } catch (error: any) {
    console.error('从云端恢复工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取同步状态处理
async function handleGetSyncStatus(sendResponse: (response: any) => void) {
  try {
    const settings = await chrome.storage.sync.get(['cloudSyncEnabled']);
    const deviceId = await getDeviceId();
    const lastSync = await chrome.storage.local.get(['lastSyncTime']);

    // 获取云端工作区列表
    const allCloudData = await chrome.storage.sync.get();
    const cloudWorkspaces = Object.keys(allCloudData)
      .filter(key => key.startsWith('cloud_workspace_'))
      .map(key => ({
        workspaceId: key.replace('cloud_workspace_', ''),
        timestamp: allCloudData[key].timestamp,
        deviceId: allCloudData[key].deviceId
      }));

    sendResponse({
      success: true,
      status: {
        enabled: settings.cloudSyncEnabled || false,
        deviceId,
        lastSyncTime: lastSync.lastSyncTime,
        cloudWorkspaces,
        localWorkspaces: Array.from(workspaces.keys())
      }
    });
  } catch (error: any) {
    console.error('获取同步状态失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 启用/禁用云同步处理
async function handleEnableCloudSync(enabled: boolean, sendResponse: (response: any) => void) {
  try {
    await chrome.storage.sync.set({ cloudSyncEnabled: enabled });

    if (enabled) {
      // 启用时同步所有工作区
      for (const workspaceId of workspaces.keys()) {
        await syncWorkspaceToCloud(workspaceId);
      }
    }

    sendResponse({
      success: true,
      message: enabled ? '云同步已启用' : '云同步已禁用',
      enabled
    });
  } catch (error: any) {
    console.error('设置云同步状态失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取当前标签页（增强版本，支持状态验证和错误恢复）
async function handleGetCurrentTabs(sendResponse: (response: any) => void) {
  try {
    console.log('🔄 获取当前标签页...');

    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 获取到 ${tabs.length} 个标签页`);

    // 验证和增强标签页数据
    const enhancedTabs = await enhanceTabsData(tabs);

    // 验证标签页状态一致性
    const verifiedTabs = await verifyTabsConsistency(enhancedTabs);

    console.log(`✅ 标签页数据处理完成: ${verifiedTabs.length} 个有效标签页`);

    sendResponse({
      success: true,
      tabs: verifiedTabs,
      metadata: {
        totalTabs: tabs.length,
        validTabs: verifiedTabs.length,
        timestamp: Date.now(),
        windowId: tabs[0]?.windowId
      }
    });

  } catch (error: any) {
    console.error('❌ 获取标签页失败:', error);

    // 尝试恢复机制
    try {
      console.log('🔄 尝试恢复机制...');
      const fallbackTabs = await getFallbackTabs();
      sendResponse({
        success: true,
        tabs: fallbackTabs,
        warning: '使用恢复机制获取标签页',
        metadata: {
          totalTabs: fallbackTabs.length,
          validTabs: fallbackTabs.length,
          timestamp: Date.now(),
          fallback: true
        }
      });
    } catch (fallbackError: any) {
      console.error('❌ 恢复机制也失败:', fallbackError);
      sendResponse({
        success: false,
        error: error.message,
        fallbackError: fallbackError.message
      });
    }
  }
}

// 增强标签页数据
async function enhanceTabsData(tabs: chrome.tabs.Tab[]) {
  const enhancedTabs = [];

  for (const tab of tabs) {
    try {
      // 基本数据验证
      if (!tab.id || !tab.url) {
        console.warn('跳过无效标签页:', tab);
        continue;
      }

      // 增强标签页数据
      const enhancedTab = {
        ...tab,
        _enhanced: true,
        _timestamp: Date.now(),
        _hostname: extractHostname(tab.url),
        _isValid: true
      };

      enhancedTabs.push(enhancedTab);

    } catch (error) {
      console.warn(`处理标签页 ${tab.id} 失败:`, error);
      // 添加错误标记但保留标签页
      enhancedTabs.push({
        ...tab,
        _enhanced: false,
        _error: error.message,
        _timestamp: Date.now(),
        _isValid: false
      });
    }
  }

  return enhancedTabs;
}

// 验证标签页一致性
async function verifyTabsConsistency(tabs: any[]) {
  const verifiedTabs = [];
  let inconsistencyCount = 0;

  for (const tab of tabs) {
    try {
      if (!tab.id) {
        continue;
      }

      // 重新获取标签页状态以确保一致性
      const actualTab = await chrome.tabs.get(tab.id);

      // 检查关键状态是否一致
      const hasInconsistency =
        tab.pinned !== actualTab.pinned ||
        tab.title !== actualTab.title ||
        tab.url !== actualTab.url;

      if (hasInconsistency) {
        inconsistencyCount++;
        console.warn(`发现状态不一致的标签页 ${tab.id}:`, {
          cached: { pinned: tab.pinned, title: tab.title },
          actual: { pinned: actualTab.pinned, title: actualTab.title }
        });

        // 使用实际状态
        verifiedTabs.push({
          ...actualTab,
          _verified: true,
          _corrected: true,
          _originalState: {
            pinned: tab.pinned,
            title: tab.title,
            url: tab.url
          }
        });
      } else {
        verifiedTabs.push({
          ...tab,
          _verified: true,
          _corrected: false
        });
      }

    } catch (error) {
      console.warn(`验证标签页 ${tab.id} 失败:`, error);
      // 标签页可能已被关闭，跳过
      continue;
    }
  }

  if (inconsistencyCount > 0) {
    console.warn(`⚠️ 发现并修正了 ${inconsistencyCount} 个状态不一致的标签页`);
  }

  return verifiedTabs;
}

// 获取后备标签页数据
async function getFallbackTabs() {
  try {
    // 尝试从所有窗口获取标签页
    const allTabs = await chrome.tabs.query({});

    // 获取当前活动窗口
    const currentWindow = await chrome.windows.getCurrent();

    // 过滤当前窗口的标签页
    const currentWindowTabs = allTabs.filter(tab => tab.windowId === currentWindow.id);

    console.log(`🔄 后备机制获取到 ${currentWindowTabs.length} 个标签页`);
    return currentWindowTabs;

  } catch (error) {
    console.error('后备机制失败:', error);
    return [];
  }
}

// 提取主机名
function extractHostname(url: string): string {
  try {
    return new URL(url).hostname;
  } catch (error) {
    return url;
  }
}

// 获取当前工作区
async function handleGetCurrentWorkspace(sendResponse: (response: any) => void) {
  try {
    sendResponse({
      success: true,
      workspaceId: currentWorkspaceId
    });
  } catch (error: any) {
    console.error('获取当前工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取所有工作区
async function handleGetWorkspaces(sendResponse: (response: any) => void) {
  try {
    const workspacesArray = Array.from(workspaces.values());
    sendResponse({ success: true, workspaces: workspacesArray });
  } catch (error: any) {
    console.error('获取工作区列表失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取特定工作区
async function handleGetWorkspace(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    const workspace = workspaces.get(workspaceId);
    if (workspace) {
      sendResponse({ success: true, workspace });
    } else {
      sendResponse({ success: false, error: '工作区不存在' });
    }
  } catch (error: any) {
    console.error('获取工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 切换工作区
async function handleSwitchWorkspace(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    await switchToWorkspace(workspaceId);
    sendResponse({ success: true, workspaceId });
  } catch (error: any) {
    console.error('切换工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 创建新工作区
async function handleCreateWorkspace(data: any, sendResponse: (response: any) => void) {
  try {
    const newWorkspace: Workspace = {
      id: generateWorkspaceId(),
      name: data.name,
      icon: data.icon || '📁',
      color: data.color || '#3B82F6',
      groups: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    workspaces.set(newWorkspace.id, newWorkspace);
    await saveWorkspacesToStorage();

    sendResponse({ success: true, workspace: newWorkspace });
  } catch (error: any) {
    console.error('创建工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// AI自动分组预览
async function handleAIAutoGroupPreview(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });

    // 简单的AI分组逻辑（基于域名）
    const groups = await performSimpleAIGrouping(tabs);

    sendResponse({ success: true, groups });
  } catch (error: any) {
    console.error('AI自动分组预览失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// AI自动分组应用
async function handleAIAutoGroupApply(data: any, sendResponse: (response: any) => void) {
  try {
    const { workspaceId, groups } = data;

    // AI分组功能已改为基于固定状态
    await applyAIPinning(groups);

    sendResponse({ success: true, message: `已创建 ${groups.length} 个分组` });
  } catch (error: any) {
    console.error('AI自动分组应用失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 添加标签页到工作区
async function handleAddTabToWorkspace(data: any, sendResponse: (response: any) => void) {
  try {
    const { workspaceId, url } = data;

    // 创建新标签页
    const tab = await chrome.tabs.create({ url, active: false });

    sendResponse({ success: true, tab });
  } catch (error: any) {
    console.error('添加标签页失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// AI分析URL
async function handleAIAnalyzeURL(data: any, sendResponse: (response: any) => void) {
  try {
    const { url } = data;

    // 创建临时标签页来获取网站信息
    const tab = await chrome.tabs.create({ url, active: false });

    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 获取页面标题
    const updatedTab = await chrome.tabs.get(tab.id!);
    const title = updatedTab.title || new URL(url).hostname;

    // AI分析网站类型和建议分组
    const domain = new URL(url).hostname;
    const category = categorizeWebsite(domain);
    const suggestedGroup = {
      name: category,
      color: getCategoryColor(category)
    };

    // 关闭临时标签页
    await chrome.tabs.remove(tab.id!);

    sendResponse({
      success: true,
      title,
      suggestedGroup,
      category
    });
  } catch (error: any) {
    console.error('AI分析URL失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 核心工作区功能实现

// ========== 重构的工作区切换逻辑 ==========

/**
 * 增强的工作区切换功能 - 集成新的统一恢复架构
 */
async function switchToWorkspace(workspaceId: string) {
  const switchStartTime = Date.now();
  let rollbackData: any = null;

  try {
    console.log('🔄 [WORKSPACE-SWITCH] ========== 开始工作区切换 ==========');
    console.log('🔄 [WORKSPACE-SWITCH] 目标工作区:', workspaceId);
    console.log('🔄 [WORKSPACE-SWITCH] 当前工作区:', currentWorkspaceId);

    // 如果切换到相同工作区，直接返回
    if (currentWorkspaceId === workspaceId) {
      console.log('🔄 [WORKSPACE-SWITCH] 已在目标工作区，无需切换');
      return;
    }

    // 验证目标工作区是否存在
    const targetWorkspace = workspaces.get(workspaceId);
    if (!targetWorkspace) {
      throw new Error(`工作区 ${workspaceId} 不存在`);
    }

    // 准备回滚数据
    rollbackData = {
      previousWorkspaceId: currentWorkspaceId,
      timestamp: switchStartTime
    };

    console.log(`📋 [WORKSPACE-SWITCH] 从 ${currentWorkspaceId} 切换到 ${workspaceId}`);

    // 阶段1: 保存当前工作区的完整状态（包括固定状态）
    console.log('💾 [WORKSPACE-SWITCH] 阶段1: 保存当前工作区完整状态...');
    await executeWithRetry(() => saveEnhancedWorkspaceState(currentWorkspaceId), 2, '保存工作区状态');

    // 阶段2: 智能挂起当前工作区的标签页
    console.log('😴 [WORKSPACE-SWITCH] 阶段2: 智能挂起当前标签页...');
    await executeWithRetry(() => suspendCurrentWorkspaceTabs(), 2, '挂起标签页');

    // 阶段3: 更新当前工作区ID
    console.log('🔄 [WORKSPACE-SWITCH] 阶段3: 更新工作区ID...');
    const previousWorkspaceId = currentWorkspaceId;
    currentWorkspaceId = workspaceId;
    await saveWorkspacesToStorage();

    // 阶段4: 统一标签页恢复（新架构 - 消除重复创建问题）
    console.log('🔄 [WORKSPACE-SWITCH] 阶段4: 统一标签页恢复...');
    const restorationResult = await executeWithRetry(
      () => unifiedTabRestoration(targetWorkspace),
      2,
      '统一标签页恢复'
    );

    // 阶段5: 恢复目标工作区的固定状态
    console.log('📌 [WORKSPACE-SWITCH] 阶段5: 恢复固定状态...');
    await executeWithRetry(() => restoreWorkspacePinStates(workspaceId), 2, '恢复固定状态');

    // 阶段6: 验证切换结果
    console.log('🔍 [WORKSPACE-SWITCH] 阶段6: 验证切换结果...');
    const verificationResult = await verifyEnhancedWorkspaceSwitchResult(workspaceId, restorationResult);

    if (!verificationResult.success) {
      console.warn('⚠️ [WORKSPACE-SWITCH] 切换验证失败:', verificationResult.issues);
      // 尝试修复问题
      await fixWorkspaceSwitchIssues(verificationResult.issues);
    }

    // 阶段7: 同步到云端（如果启用，非阻塞）
    console.log('☁️ [WORKSPACE-SWITCH] 阶段7: 同步到云端...');
    syncWorkspaceToCloud(workspaceId).catch(error => {
      console.warn('⚠️ [WORKSPACE-SWITCH] 云同步失败（非致命错误）:', error);
    });

    const switchDuration = Date.now() - switchStartTime;
    console.log(`🎉 [WORKSPACE-SWITCH] ========== 切换完成 (${switchDuration}ms) ==========`);
    console.log(`🎉 [WORKSPACE-SWITCH] 目标工作区: ${targetWorkspace.name}`);
    console.log(`🎉 [WORKSPACE-SWITCH] 恢复结果: 成功 ${restorationResult?.restoredCount || 0}, 跳过 ${restorationResult?.skippedCount || 0}`);

    // 记录切换历史
    await recordWorkspaceSwitchHistory(previousWorkspaceId, workspaceId, switchDuration, restorationResult);

  } catch (error) {
    console.error('❌ [WORKSPACE-SWITCH] 工作区切换失败:', error);

    // 尝试回滚
    if (rollbackData) {
      console.log('🔄 [WORKSPACE-SWITCH] 尝试回滚工作区切换...');
      await rollbackWorkspaceSwitch(rollbackData);
    }

    throw error;
  }
}

/**
 * 保存增强的工作区状态（包括固定状态）
 */
async function saveEnhancedWorkspaceState(workspaceId: string) {
  try {
    console.log(`💾 [SAVE-STATE] 保存工作区 ${workspaceId} 的增强状态...`);

    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`💾 [SAVE-STATE] 当前有 ${currentTabs.length} 个标签页`);

    // 保存标签页的详细状态
    const tabStates = currentTabs.map(tab => ({
      id: tab.id,
      url: tab.url,
      title: tab.title,
      pinned: tab.pinned,
      active: tab.active,
      index: tab.index,
      groupId: tab.groupId,
      favIconUrl: tab.favIconUrl
    }));

    // 保存固定状态映射
    const pinStates: Record<string, boolean> = {};
    currentTabs.forEach(tab => {
      if (tab.url) {
        pinStates[tab.url] = tab.pinned || false;
      }
    });

    // 保存到存储
    const stateData = {
      workspaceId,
      tabs: tabStates,
      pinStates,
      timestamp: new Date().toISOString(),
      tabCount: currentTabs.length
    };

    await chrome.storage.local.set({
      [`workspaceState_${workspaceId}`]: stateData,
      [`pinStates_${workspaceId}`]: pinStates
    });

    console.log(`✅ [SAVE-STATE] 已保存工作区 ${workspaceId} 的状态: ${currentTabs.length} 个标签页`);
    console.log(`✅ [SAVE-STATE] 固定状态: ${Object.keys(pinStates).length} 个URL`);

  } catch (error) {
    console.error('❌ [SAVE-STATE] 保存工作区状态失败:', error);
    throw error;
  }
}

/**
 * 恢复工作区的固定状态
 */
async function restoreWorkspacePinStates(workspaceId: string) {
  try {
    console.log(`📌 [RESTORE-PIN] 恢复工作区 ${workspaceId} 的固定状态...`);

    // 获取保存的固定状态
    const result = await chrome.storage.local.get([`pinStates_${workspaceId}`]);
    const savedPinStates = result[`pinStates_${workspaceId}`] || {};

    if (Object.keys(savedPinStates).length === 0) {
      console.log('📌 [RESTORE-PIN] 没有保存的固定状态');
      return;
    }

    console.log(`📌 [RESTORE-PIN] 找到 ${Object.keys(savedPinStates).length} 个保存的固定状态`);

    // 获取当前标签页
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    let updatedCount = 0;

    for (const tab of currentTabs) {
      if (tab.url && tab.id) {
        const shouldBePinned = savedPinStates[tab.url];

        if (shouldBePinned !== undefined && tab.pinned !== shouldBePinned) {
          try {
            await chrome.tabs.update(tab.id, { pinned: shouldBePinned });
            updatedCount++;
            console.log(`📌 [RESTORE-PIN] 更新固定状态: ${tab.title} -> ${shouldBePinned ? '固定' : '取消固定'}`);
          } catch (error) {
            console.warn(`⚠️ [RESTORE-PIN] 更新标签页 ${tab.id} 固定状态失败:`, error);
          }
        }
      }
    }

    console.log(`✅ [RESTORE-PIN] 已更新 ${updatedCount} 个标签页的固定状态`);

  } catch (error) {
    console.error('❌ [RESTORE-PIN] 恢复固定状态失败:', error);
    throw error;
  }
}

/**
 * 增强的工作区切换结果验证
 */
async function verifyEnhancedWorkspaceSwitchResult(workspaceId: string, restorationResult: any): Promise<{
  success: boolean;
  issues: string[];
  metrics: {
    expectedTabs: number;
    actualTabs: number;
    restoredTabs: number;
    skippedTabs: number;
    pinnedTabs: number;
  };
}> {
  try {
    console.log(`🔍 [VERIFY-SWITCH] 验证工作区 ${workspaceId} 的切换结果...`);

    const issues: string[] = [];
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const workspace = workspaces.get(workspaceId);

    // 计算预期的标签页数量
    const expectedTabs = await calculateExpectedTabCount(workspaceId);
    const actualTabs = currentTabs.length;
    const restoredTabs = restorationResult?.restoredCount || 0;
    const skippedTabs = restorationResult?.skippedCount || 0;
    const pinnedTabs = currentTabs.filter(tab => tab.pinned).length;

    console.log(`🔍 [VERIFY-SWITCH] 标签页统计:`, {
      expected: expectedTabs,
      actual: actualTabs,
      restored: restoredTabs,
      skipped: skippedTabs,
      pinned: pinnedTabs
    });

    // 验证1: 检查是否有预期的核心标签页
    const coreTabsResult = await verifyCoreTabsPresent(workspaceId);
    if (!coreTabsResult.success) {
      issues.push(`缺少核心标签页: ${coreTabsResult.missing.join(', ')}`);
    }

    // 验证2: 检查固定状态是否正确
    const pinStatesResult = await verifyPinStates(workspaceId);
    if (!pinStatesResult.success) {
      issues.push(`固定状态不正确: ${pinStatesResult.issues.join(', ')}`);
    }

    // 验证3: 检查是否有重复的标签页
    const duplicatesResult = await verifyNoDuplicateTabs();
    if (!duplicatesResult.success) {
      issues.push(`发现重复标签页: ${duplicatesResult.duplicates.length} 组`);
    }

    const success = issues.length === 0;
    console.log(`🔍 [VERIFY-SWITCH] 验证结果: ${success ? '通过' : '失败'}`);
    if (!success) {
      console.log(`🔍 [VERIFY-SWITCH] 问题列表:`, issues);
    }

    return {
      success,
      issues,
      metrics: {
        expectedTabs,
        actualTabs,
        restoredTabs,
        skippedTabs,
        pinnedTabs
      }
    };

  } catch (error) {
    console.error('❌ [VERIFY-SWITCH] 验证工作区切换结果失败:', error);
    return {
      success: false,
      issues: [`验证过程异常: ${(error as Error).message}`],
      metrics: {
        expectedTabs: 0,
        actualTabs: 0,
        restoredTabs: 0,
        skippedTabs: 0,
        pinnedTabs: 0
      }
    };
  }
}

/**
 * 计算预期的标签页数量
 */
async function calculateExpectedTabCount(workspaceId: string): Promise<number> {
  try {
    const targetTabs = await getWorkspaceTargetTabs(workspaces.get(workspaceId)!);
    return targetTabs.length;
  } catch {
    return 0;
  }
}

/**
 * 验证核心标签页是否存在
 */
async function verifyCoreTabsPresent(workspaceId: string): Promise<{
  success: boolean;
  missing: string[];
}> {
  try {
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const currentUrls = new Set(currentTabs.map(tab => tab.url).filter(Boolean));

    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);
    const missing: string[] = [];

    for (const urlPattern of pinnedUrls) {
      const found = Array.from(currentUrls).some(url =>
        url.includes(urlPattern) || urlPattern.includes(url)
      );

      if (!found) {
        missing.push(urlPattern);
      }
    }

    return {
      success: missing.length === 0,
      missing
    };
  } catch (error) {
    console.error('验证核心标签页失败:', error);
    return { success: false, missing: [] };
  }
}

/**
 * 验证固定状态
 */
async function verifyPinStates(workspaceId: string): Promise<{
  success: boolean;
  issues: string[];
}> {
  try {
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);
    const issues: string[] = [];

    for (const tab of currentTabs) {
      if (tab.url) {
        const shouldBePinned = pinnedUrls.some(pattern => tab.url!.includes(pattern));

        if (shouldBePinned && !tab.pinned) {
          issues.push(`${tab.title} 应该被固定但未固定`);
        }
      }
    }

    return {
      success: issues.length === 0,
      issues
    };
  } catch (error) {
    console.error('验证固定状态失败:', error);
    return { success: false, issues: [] };
  }
}

/**
 * 验证没有重复的标签页
 */
async function verifyNoDuplicateTabs(): Promise<{
  success: boolean;
  duplicates: Array<{ url: string; count: number; tabIds: number[] }>;
}> {
  try {
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const urlMap = new Map<string, number[]>();

    currentTabs.forEach(tab => {
      if (tab.url && tab.id) {
        const normalizedUrl = smartNormalizeUrl(tab.url);
        if (!urlMap.has(normalizedUrl)) {
          urlMap.set(normalizedUrl, []);
        }
        urlMap.get(normalizedUrl)!.push(tab.id);
      }
    });

    const duplicates = Array.from(urlMap.entries())
      .filter(([_, tabIds]) => tabIds.length > 1)
      .map(([url, tabIds]) => ({
        url,
        count: tabIds.length,
        tabIds
      }));

    return {
      success: duplicates.length === 0,
      duplicates
    };
  } catch (error) {
    console.error('验证重复标签页失败:', error);
    return { success: false, duplicates: [] };
  }
}

// 带重试的执行函数
async function executeWithRetry(operation: () => Promise<void>, maxRetries: number, operationName: string) {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      await operation();
      if (attempt > 1) {
        console.log(`✅ ${operationName} 重试成功 (第${attempt}次尝试)`);
      }
      return;
    } catch (error) {
      lastError = error as Error;
      console.warn(`⚠️ ${operationName} 失败 (第${attempt}次尝试):`, error);

      if (attempt <= maxRetries) {
        const delay = Math.min(1000 * attempt, 3000); // 递增延迟，最大3秒
        console.log(`⏳ ${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(`${operationName} 在 ${maxRetries + 1} 次尝试后仍然失败: ${lastError?.message}`);
}

// 验证工作区切换结果
async function verifyWorkspaceSwitchResult(workspaceId: string) {
  try {
    const issues: string[] = [];

    // 验证当前工作区ID
    if (currentWorkspaceId !== workspaceId) {
      issues.push(`工作区ID不匹配: 期望 ${workspaceId}, 实际 ${currentWorkspaceId}`);
    }

    // 验证标签页状态
    const tabs = await chrome.tabs.query({ currentWindow: true });
    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);

    let pinningIssues = 0;
    for (const tab of tabs) {
      if (!tab.url) continue;

      const shouldBePinned = pinnedUrls.some(pattern => tab.url!.includes(pattern));
      const actuallyPinned = tab.pinned || false;

      if (shouldBePinned !== actuallyPinned) {
        pinningIssues++;
      }
    }

    if (pinningIssues > 0) {
      issues.push(`${pinningIssues} 个标签页的固定状态不正确`);
    }

    return {
      success: issues.length === 0,
      issues
    };

  } catch (error) {
    return {
      success: false,
      issues: [`验证过程失败: ${(error as Error).message}`]
    };
  }
}

// 修复工作区切换问题
async function fixWorkspaceSwitchIssues(issues: string[]) {
  console.log('🔧 尝试修复工作区切换问题:', issues);

  try {
    // 重新应用固定设置
    const workspace = workspaces.get(currentWorkspaceId);
    if (workspace) {
      await applyWorkspacePinning(workspace);
    }

    console.log('✅ 工作区切换问题修复完成');
  } catch (error) {
    console.error('❌ 修复工作区切换问题失败:', error);
  }
}

// 回滚工作区切换
async function rollbackWorkspaceSwitch(rollbackData: any) {
  try {
    console.log('🔄 回滚工作区切换到:', rollbackData.previousWorkspaceId);

    // 恢复工作区ID
    currentWorkspaceId = rollbackData.previousWorkspaceId;
    await saveWorkspacesToStorage();

    // 尝试恢复之前的会话
    await restoreWorkspaceSession(rollbackData.previousWorkspaceId);

    console.log('✅ 工作区切换回滚完成');
  } catch (rollbackError) {
    console.error('❌ 工作区切换回滚失败:', rollbackError);
  }
}

/**
 * 记录增强的工作区切换历史
 */
async function recordWorkspaceSwitchHistory(
  fromWorkspaceId: string,
  toWorkspaceId: string,
  duration: number,
  restorationResult?: any
) {
  try {
    const historyEntry = {
      from: fromWorkspaceId,
      to: toWorkspaceId,
      timestamp: Date.now(),
      duration,
      success: true,
      restorationStats: {
        restoredCount: restorationResult?.restoredCount || 0,
        skippedCount: restorationResult?.skippedCount || 0,
        errorCount: restorationResult?.errors?.length || 0
      },
      fromWorkspaceName: workspaces.get(fromWorkspaceId)?.name || fromWorkspaceId,
      toWorkspaceName: workspaces.get(toWorkspaceId)?.name || toWorkspaceId
    };

    // 获取现有历史
    const result = await chrome.storage.local.get(['workspaceSwitchHistory']);
    const history = result.workspaceSwitchHistory || [];

    // 添加新记录
    history.push(historyEntry);

    // 保留最近100条记录
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    // 保存历史
    await chrome.storage.local.set({ workspaceSwitchHistory: history });

    console.log('📊 [SWITCH-HISTORY] 已记录切换历史:', {
      from: historyEntry.fromWorkspaceName,
      to: historyEntry.toWorkspaceName,
      duration: `${duration}ms`,
      restored: restorationResult?.restoredCount || 0
    });

  } catch (error) {
    console.warn('⚠️ [SWITCH-HISTORY] 记录工作区切换历史失败:', error);
  }
}

// 保存当前会话状态（增强版本，支持数据验证和压缩）
async function saveCurrentSessionState() {
  try {
    console.log('💾 开始保存当前会话状态...');

    const currentWindow = await chrome.windows.getCurrent({ populate: true });
    if (!currentWindow.tabs) {
      throw new Error('无法获取当前窗口的标签页');
    }

    console.log(`📋 当前窗口有 ${currentWindow.tabs.length} 个标签页`);

    // 验证和清理标签页数据
    const validTabs = currentWindow.tabs.filter(tab => tab.id && tab.url);
    console.log(`✅ 有效标签页: ${validTabs.length} 个`);

    // 创建会话数据
    const sessionData = {
      windowId: currentWindow.id,
      workspaceId: currentWorkspaceId,
      timestamp: Date.now(),
      version: '2.0', // 版本标识
      metadata: {
        totalTabs: currentWindow.tabs.length,
        validTabs: validTabs.length,
        pinnedTabs: validTabs.filter(tab => tab.pinned).length,
        activeTabs: validTabs.filter(tab => tab.active).length,
        discardedTabs: validTabs.filter(tab => tab.discarded).length
      },
      tabs: await Promise.all(validTabs.map(async tab => ({
        id: tab.id,
        url: tab.url,
        title: tab.title || '',
        favIconUrl: tab.favIconUrl,
        pinned: tab.pinned || false,
        active: tab.active || false,
        index: tab.index || 0,
        discarded: tab.discarded || false,
        groupId: tab.groupId,
        // 添加额外的状态信息
        lastAccessed: Date.now(),
        hostname: extractHostname(tab.url || ''),
        isImportant: await isImportantTab(tab)
      })))
    };

    // 压缩会话数据（如果标签页过多）
    const compressedSessionData = await compressSessionData(sessionData);

    // 保存到本地存储
    const storageKey = `session_${currentWorkspaceId}`;
    await chrome.storage.local.set({
      [storageKey]: compressedSessionData,
      [`${storageKey}_backup`]: sessionData, // 保留未压缩的备份
      lastSessionSave: Date.now(),
      sessionSaveCount: await getSessionSaveCount() + 1
    });

    console.log(`✅ 会话状态已保存: ${sessionData.tabs.length} 个标签页 (工作区: ${currentWorkspaceId})`);

    // 清理旧的会话数据
    await cleanupOldSessionData();

  } catch (error) {
    console.error('❌ 保存会话状态失败:', error);

    // 尝试简化保存
    await saveSimplifiedSessionState();
  }
}

// 判断是否为重要标签页
async function isImportantTab(tab: chrome.tabs.Tab): Promise<boolean> {
  try {
    if (!tab.url) return false;

    // 重要网站列表
    const importantDomains = [
      'gmail.com', 'docs.google.com', 'github.com', 'stackoverflow.com',
      'notion.so', 'figma.com', 'slack.com', 'discord.com',
      'yuque.com', 'feishu.cn'
    ];

    // 检查是否为重要域名
    const isImportantDomain = importantDomains.some(domain => tab.url!.includes(domain));

    // 检查是否包含表单或编辑内容
    const hasEditContent = tab.url.includes('edit') ||
                          tab.url.includes('compose') ||
                          tab.url.includes('new') ||
                          tab.url.includes('create');

    return isImportantDomain || hasEditContent || tab.pinned || false;

  } catch (error) {
    console.warn('判断标签页重要性失败:', error);
    return false;
  }
}

// 压缩会话数据
async function compressSessionData(sessionData: any) {
  try {
    // 如果标签页数量不多，不需要压缩
    if (sessionData.tabs.length <= 20) {
      return sessionData;
    }

    console.log('🗜️ 压缩会话数据...');

    // 保留重要标签页的完整信息，其他标签页只保留基本信息
    const compressedTabs = sessionData.tabs.map((tab: any) => {
      if (tab.isImportant || tab.pinned || tab.active) {
        return tab; // 保留完整信息
      } else {
        // 只保留基本信息
        return {
          id: tab.id,
          url: tab.url,
          title: tab.title,
          pinned: tab.pinned,
          active: tab.active,
          index: tab.index,
          _compressed: true
        };
      }
    });

    return {
      ...sessionData,
      tabs: compressedTabs,
      _compressed: true,
      _originalTabCount: sessionData.tabs.length
    };

  } catch (error) {
    console.warn('压缩会话数据失败:', error);
    return sessionData;
  }
}

// 获取会话保存次数
async function getSessionSaveCount(): Promise<number> {
  try {
    const result = await chrome.storage.local.get(['sessionSaveCount']);
    return result.sessionSaveCount || 0;
  } catch (error) {
    return 0;
  }
}

// 保存简化的会话状态（后备方案）
async function saveSimplifiedSessionState() {
  try {
    console.log('🔄 保存简化会话状态...');

    const tabs = await chrome.tabs.query({ currentWindow: true });
    const simplifiedData = {
      workspaceId: currentWorkspaceId,
      timestamp: Date.now(),
      simplified: true,
      tabUrls: tabs.filter(tab => tab.url && !tab.url.startsWith('chrome://')).map(tab => tab.url)
    };

    await chrome.storage.local.set({
      [`session_${currentWorkspaceId}_simple`]: simplifiedData
    });

    console.log(`✅ 简化会话状态已保存: ${simplifiedData.tabUrls.length} 个URL`);

  } catch (error) {
    console.error('❌ 保存简化会话状态也失败:', error);
  }
}

// 清理旧的会话数据
async function cleanupOldSessionData() {
  try {
    const result = await chrome.storage.local.get();
    const keysToDelete: string[] = [];
    const currentTime = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

    for (const [key, value] of Object.entries(result)) {
      if (key.startsWith('session_') && typeof value === 'object' && value !== null) {
        const sessionData = value as any;
        if (sessionData.timestamp && currentTime - sessionData.timestamp > maxAge) {
          keysToDelete.push(key);
        }
      }
    }

    if (keysToDelete.length > 0) {
      await chrome.storage.local.remove(keysToDelete);
      console.log(`🧹 清理了 ${keysToDelete.length} 个过期会话数据`);
    }

  } catch (error) {
    console.warn('清理旧会话数据失败:', error);
  }
}

// 智能挂起当前工作区的标签页（修复版本，保持固定状态）
async function suspendCurrentWorkspaceTabs() {
  try {
    console.log(`😴 [DEBUG-SUSPEND] ========== 开始智能挂起当前工作区标签页 ==========`);
    console.log(`😴 [DEBUG-SUSPEND] 当前工作区ID: ${currentWorkspaceId}`);

    const allTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [DEBUG-SUSPEND] 当前窗口共有 ${allTabs.length} 个标签页`);

    // 保存当前标签页的固定状态，以便后续恢复
    const tabPinnedStates = new Map<number, boolean>();

    // 记录挂起前的标签页状态
    console.log(`📋 [DEBUG-SUSPEND] ========== 挂起前标签页状态 ==========`);
    allTabs.forEach((tab, index) => {
      if (tab.id) {
        tabPinnedStates.set(tab.id, tab.pinned || false);
      }

      console.log(`📋 [DEBUG-SUSPEND] 标签页 ${index + 1}: ${tab.title}`);
      console.log(`📋 [DEBUG-SUSPEND]   - URL: ${tab.url}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 活跃状态: ${tab.active ? '活跃' : '非活跃'}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 挂起状态: ${tab.discarded ? '已挂起' : '未挂起'}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 标签页ID: ${tab.id}`);
    });

    // 保存固定状态到存储，以便工作区切换后恢复
    await chrome.storage.local.set({
      [`pinnedStates_${currentWorkspaceId}`]: Object.fromEntries(tabPinnedStates)
    });
    console.log(`💾 [DEBUG-SUSPEND] 已保存工作区 ${currentWorkspaceId} 的固定状态`);

    // 智能筛选需要挂起的标签页（只挂起非活跃的标签页）
    const suspensionCandidates = allTabs.filter(tab => {
      // 不挂起活跃标签页
      if (tab.active) {
        console.log(`⏭️ [DEBUG-SUSPEND] 跳过活跃标签页: ${tab.title}`);
        return false;
      }

      // 不挂起已经挂起的标签页
      if (tab.discarded) {
        console.log(`⏭️ [DEBUG-SUSPEND] 跳过已挂起标签页: ${tab.title}`);
        return false;
      }

      // 不挂起Chrome内部页面
      if (tab.url && (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://'))) {
        console.log(`⏭️ [DEBUG-SUSPEND] 跳过Chrome内部页面: ${tab.title}`);
        return false;
      }

      return true;
    });

    console.log(`🎯 [DEBUG-SUSPEND] 识别出 ${suspensionCandidates.length} 个挂起候选标签页`);

    if (suspensionCandidates.length === 0) {
      console.log('📝 [DEBUG-SUSPEND] 没有需要挂起的标签页');
      return;
    }

    // 执行批量挂起操作
    const suspensionResults = await performBatchSuspension(suspensionCandidates);

    // 验证挂起结果
    await verifySuspensionResults(suspensionResults);

    console.log(`✅ 智能挂起完成: ${suspensionResults.successful}/${suspensionCandidates.length} 个标签页已挂起`);

  } catch (error) {
    console.error('❌ 智能挂起标签页失败:', error);

    // 尝试简单挂起作为后备方案
    await performSimpleSuspension();
  }
}

// 识别挂起候选标签页
async function identifySuspensionCandidates(allTabs: chrome.tabs.Tab[]) {
  const candidates: chrome.tabs.Tab[] = [];
  const currentTime = Date.now();

  for (const tab of allTabs) {
    try {
      // 基本过滤条件
      if (!tab.id || tab.pinned || tab.active || tab.discarded) {
        continue;
      }

      // 跳过特殊页面
      if (!tab.url ||
          tab.url.startsWith('chrome://') ||
          tab.url.startsWith('chrome-extension://') ||
          tab.url === 'chrome://newtab/') {
        continue;
      }

      // 智能判断是否应该跳过挂起
      const shouldSkip = await shouldSkipTabSuspension(tab, currentTime, 5 * 60 * 1000);
      if (shouldSkip) {
        console.log(`⏭️ 跳过挂起重要标签页: ${tab.title}`);
        continue;
      }

      candidates.push(tab);

    } catch (error) {
      console.warn(`评估标签页 ${tab.id} 挂起条件失败:`, error);
    }
  }

  return candidates;
}

// 执行批量挂起操作
async function performBatchSuspension(tabs: chrome.tabs.Tab[]) {
  const results = {
    successful: 0,
    failed: 0,
    errors: [] as Array<{tabId: number, title: string, error: string}>
  };

  // 分批处理，避免同时挂起太多标签页
  const batchSize = 5;
  const batches = [];

  for (let i = 0; i < tabs.length; i += batchSize) {
    batches.push(tabs.slice(i, i + batchSize));
  }

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    console.log(`😴 处理第 ${batchIndex + 1}/${batches.length} 批标签页 (${batch.length} 个)`);

    // 并行处理当前批次
    const batchPromises = batch.map(async (tab) => {
      try {
        if (tab.id) {
          await chrome.tabs.discard(tab.id);
          results.successful++;
          console.log(`✅ 已挂起: ${tab.title}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          tabId: tab.id || 0,
          title: tab.title || '未知标题',
          error: (error as Error).message
        });
        console.warn(`❌ 挂起失败: ${tab.title}`, error);
      }
    });

    await Promise.all(batchPromises);

    // 批次间短暂延迟
    if (batchIndex < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  return results;
}

// 验证挂起结果
async function verifySuspensionResults(results: any) {
  try {
    if (results.failed > 0) {
      console.warn(`⚠️ ${results.failed} 个标签页挂起失败:`, results.errors);
    }

    // 等待一段时间后验证挂起状态
    await new Promise(resolve => setTimeout(resolve, 1000));

    const allTabs = await chrome.tabs.query({ currentWindow: true });
    const discardedCount = allTabs.filter(tab => tab.discarded).length;
    const totalTabs = allTabs.length;

    console.log(`📊 挂起验证结果: ${discardedCount}/${totalTabs} 个标签页已挂起`);

    // 如果挂起比例过低，记录警告
    if (totalTabs > 5 && discardedCount / totalTabs < 0.2) {
      console.warn('⚠️ 挂起比例较低，可能存在问题');
    }

  } catch (error) {
    console.error('验证挂起结果失败:', error);
  }
}

// 简单挂起作为后备方案
async function performSimpleSuspension() {
  try {
    console.log('🔄 执行简单挂起后备方案...');

    const allTabs = await chrome.tabs.query({
      currentWindow: true,
      pinned: false,
      active: false,
      discarded: false
    });

    let suspendedCount = 0;
    for (const tab of allTabs) {
      try {
        if (tab.id && tab.url && !tab.url.startsWith('chrome://')) {
          await chrome.tabs.discard(tab.id);
          suspendedCount++;
        }
      } catch (error) {
        console.warn(`简单挂起失败: ${tab.title}`, error);
      }
    }

    console.log(`✅ 简单挂起完成: ${suspendedCount} 个标签页`);

  } catch (error) {
    console.error('❌ 简单挂起也失败:', error);
  }
}

// 恢复工作区会话
async function restoreWorkspaceSession(workspaceId: string) {
  try {
    console.log('🔄 恢复工作区会话:', workspaceId);

    // 尝试从本地存储恢复会话
    const sessionKey = `session_${workspaceId}`;
    const result = await chrome.storage.local.get([sessionKey]);
    const sessionData = result[sessionKey];

    if (sessionData && sessionData.tabs && sessionData.tabs.length > 0) {
      console.log(`📋 找到保存的会话: ${sessionData.tabs.length} 个标签页`);
      await restoreTabsFromSession(sessionData.tabs);
    } else {
      console.log('📋 没有找到保存的会话，使用统一恢复架构');
      const workspace = workspaces.get(workspaceId);
      if (workspace) {
        await unifiedTabRestoration(workspace);
      }
    }
  } catch (error) {
    console.error('❌ 恢复工作区会话失败:', error);
  }
}

// 从会话数据恢复标签页
async function restoreTabsFromSession(sessionTabs: any[]) {
  try {
    console.log('🔄 从会话数据恢复标签页...');

    // 获取当前标签页
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const currentUrls = new Set(currentTabs.map(tab => tab.url));

    let restoredCount = 0;
    for (const sessionTab of sessionTabs) {
      // 跳过已存在的标签页
      if (currentUrls.has(sessionTab.url)) {
        continue;
      }

      try {
        const newTab = await chrome.tabs.create({
          url: sessionTab.url,
          active: false,
          pinned: sessionTab.pinned || false,
          index: sessionTab.index
        });

        if (newTab.id) {
          restoredCount++;

          // 如果原来是挂起状态，立即挂起新标签页
          if (sessionTab.discarded) {
            setTimeout(async () => {
              try {
                await chrome.tabs.discard(newTab.id!);
              } catch (error) {
                console.warn('挂起恢复的标签页失败:', error);
              }
            }, 1000);
          }
        }
      } catch (error) {
        console.warn(`恢复标签页失败: ${sessionTab.url}`, error);
      }
    }

    console.log(`✅ 从会话恢复了 ${restoredCount} 个标签页`);
  } catch (error) {
    console.error('❌ 从会话恢复标签页失败:', error);
  }
}

// ========== 旧的 restoreWorkspaceFromConfig 函数已删除，使用新的 unifiedTabRestoration ==========

// 同步工作区到云端
async function syncWorkspaceToCloud(workspaceId: string) {
  try {
    // 检查是否启用云同步
    const settings = await chrome.storage.sync.get(['cloudSyncEnabled']);
    if (!settings.cloudSyncEnabled) {
      return;
    }

    console.log('☁️ 同步工作区到云端:', workspaceId);

    const workspace = workspaces.get(workspaceId);
    if (workspace) {
      const syncData = {
        workspaceId,
        workspace,
        timestamp: Date.now(),
        deviceId: await getDeviceId()
      };

      await chrome.storage.sync.set({
        [`cloud_workspace_${workspaceId}`]: syncData
      });

      console.log('✅ 工作区已同步到云端');
    }
  } catch (error) {
    console.warn('⚠️ 云同步失败:', error);
  }
}

// 获取设备ID
async function getDeviceId(): Promise<string> {
  try {
    const result = await chrome.storage.local.get(['deviceId']);
    if (result.deviceId) {
      return result.deviceId;
    }

    const newDeviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    await chrome.storage.local.set({ deviceId: newDeviceId });
    return newDeviceId;
  } catch (error) {
    console.error('获取设备ID失败:', error);
    return 'unknown_device';
  }
}

// 保存当前工作区状态（修复版本，严格过滤临时标签页）
async function saveCurrentWorkspaceState() {
  try {
    console.log(`💾 [DEBUG-SAVE] ========== 开始保存当前工作区状态 ==========`);
    console.log(`💾 [DEBUG-SAVE] 当前工作区ID: ${currentWorkspaceId}`);

    // 如果没有当前工作区，跳过保存
    if (!currentWorkspaceId) {
      console.log(`⚠️ [DEBUG-SAVE] 没有当前工作区，跳过保存`);
      return;
    }

    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const workspace = workspaces.get(currentWorkspaceId);

    if (workspace) {
      console.log(`📋 [DEBUG-SAVE] 当前工作区: ${workspace.name}，有 ${currentTabs.length} 个标签页`);

      // 获取当前工作区的预定义固定URL模式
      const pinnedUrls = definePinnedUrlsForWorkspace(currentWorkspaceId);
      console.log(`📌 [DEBUG-SAVE] 预定义固定URL模式:`, pinnedUrls);

      // 如果没有预定义的固定URL模式，不保存任何标签页
      if (!pinnedUrls || pinnedUrls.length === 0) {
        console.log(`⚠️ [DEBUG-SAVE] 工作区没有预定义固定URL模式，不保存标签页状态`);
        return;
      }

      console.log(`🔍 [DEBUG-SAVE] ========== 分析当前标签页 ==========`);

      // 只保存真正符合预定义模式且当前为固定状态的标签页
      const validPinnedTabs = currentTabs.filter(tab => {
        if (!tab.url || !tab.pinned) {
          console.log(`⏭️ [DEBUG-SAVE] 跳过非固定标签页: ${tab.title} - 固定状态: ${tab.pinned}`);
          return false;
        }

        // 严格检查：必须同时满足预定义模式匹配AND当前为固定状态
        const matchesPredefinedPattern = pinnedUrls.some(pattern => {
          const matches = tab.url!.includes(pattern);
          console.log(`🔍 [DEBUG-SAVE] 检查模式 "${pattern}" 是否匹配 "${tab.url}": ${matches}`);
          return matches;
        });

        if (matchesPredefinedPattern && tab.pinned) {
          console.log(`✅ [DEBUG-SAVE] 保存有效的预定义固定标签页: ${tab.title} - ${tab.url}`);
          return true;
        } else {
          console.log(`⏭️ [DEBUG-SAVE] 跳过标签页: ${tab.title} - 匹配模式: ${matchesPredefinedPattern}, 固定状态: ${tab.pinned}`);
          return false;
        }
      });

      console.log(`📊 [DEBUG-SAVE] 严格过滤结果: 总共${currentTabs.length}个标签页，保存${validPinnedTabs.length}个有效固定标签页`);

      // 只有当有有效的固定标签页时才更新工作区配置
      if (validPinnedTabs.length > 0) {
        workspace.groups = await organizeTabsIntoGroups(validPinnedTabs);
        workspace.updatedAt = new Date().toISOString();
        workspaces.set(currentWorkspaceId, workspace);
        await saveWorkspacesToStorage();

        console.log(`✅ [DEBUG-SAVE] 工作区状态保存完成: 保存了 ${validPinnedTabs.length} 个有效固定标签页`);
        console.log(`✅ [DEBUG-SAVE] 工作区更新时间: ${workspace.updatedAt}`);
      } else {
        console.log(`⚠️ [DEBUG-SAVE] 没有有效的固定标签页，不更新工作区配置`);
      }
    } else {
      console.error(`❌ [DEBUG-SAVE] 工作区不存在: ${currentWorkspaceId}`);
    }
  } catch (error) {
    console.error('❌ [DEBUG-SAVE] 保存工作区状态失败:', error);
  }
}

// 应用工作区标签页可见性
async function applyWorkspaceTabVisibility(workspaceId: string) {
  try {
    console.log(`🔄 [DEBUG-VISIBILITY] ========== 开始应用工作区标签页可见性 ==========`);
    console.log(`🔄 [DEBUG-VISIBILITY] 目标工作区ID: ${workspaceId}`);

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      console.warn(`❌ [DEBUG-VISIBILITY] 工作区不存在: ${workspaceId}`);
      return;
    }

    console.log(`🔄 [DEBUG-VISIBILITY] 工作区名称: ${workspace.name}`);

    // 实现真正的工作区标签页可见性控制
    // 1. 获取当前所有标签页
    console.log(`📋 [DEBUG-VISIBILITY] 步骤1: 获取当前所有标签页...`);
    const allTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [DEBUG-VISIBILITY] 步骤1: 获取到 ${allTabs.length} 个当前标签页`);

    // 记录当前标签页状态
    allTabs.forEach((tab, index) => {
      console.log(`📋 [DEBUG-VISIBILITY] 当前标签页 ${index + 1}: ${tab.title}`);
      console.log(`📋 [DEBUG-VISIBILITY]   - URL: ${tab.url}`);
      console.log(`📋 [DEBUG-VISIBILITY]   - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
      console.log(`📋 [DEBUG-VISIBILITY]   - 挂起状态: ${tab.discarded ? '已挂起' : '未挂起'}`);
    });

    // 2. 保存当前标签页状态到全局存储
    console.log(`💾 [DEBUG-VISIBILITY] 步骤2: 保存当前标签页状态...`);
    await saveCurrentTabsState(allTabs);
    console.log(`💾 [DEBUG-VISIBILITY] 步骤2: 已保存当前标签页状态`);

    // 3. 暂停所有非固定标签页而非关闭
    console.log(`😴 [DEBUG-VISIBILITY] 步骤3: 暂停非固定标签页...`);
    await suspendAllNonPinnedTabs();
    console.log(`😴 [DEBUG-VISIBILITY] 步骤3: 已暂停非固定标签页`);

    // 4. 恢复工作区的标签页（使用新的统一恢复架构）
    console.log(`🔄 [DEBUG-VISIBILITY] 步骤4: 恢复工作区标签页...`);
    await unifiedTabRestoration(workspace);
    console.log(`🔄 [DEBUG-VISIBILITY] 步骤4: 已恢复工作区标签页`);

    // 5. 等待标签页加载完成，然后应用工作区特定的固定设置
    console.log(`📌 [DEBUG-VISIBILITY] 步骤5: 等待标签页加载完成...`);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

    console.log(`📌 [DEBUG-VISIBILITY] 步骤5: 开始应用工作区固定设置...`);
    await applyWorkspacePinning(workspace);
    console.log(`✅ [DEBUG-VISIBILITY] 工作区切换完成: ${workspace.name}`);

  } catch (error) {
    console.error(`❌ [DEBUG-VISIBILITY] 应用工作区标签页可见性失败:`, error);
    console.error(`❌ [DEBUG-VISIBILITY] 错误堆栈:`, error.stack);
  }
}

// 增强的标签页状态保存
async function saveCurrentTabsState(tabs: chrome.tabs.Tab[]) {
  try {
    const enhancedTabsState = await Promise.all(tabs.map(async (tab) => {
      const basicState = {
        id: tab.id,
        url: tab.url,
        title: tab.title,
        favIconUrl: tab.favIconUrl,
        pinned: tab.pinned,
        index: tab.index,
        active: tab.active,
        discarded: tab.discarded,
        lastAccessed: Date.now() // 记录当前时间作为最后访问时间
      };

      // 尝试获取更详细的会话信息
      try {
        if (tab.id) {
          // 使用sessions API保存更详细的状态
          const sessionInfo = await chrome.sessions.getRecentlyClosed({ maxResults: 1 });
          return {
            ...basicState,
            sessionData: sessionInfo.length > 0 ? sessionInfo[0] : null
          };
        }
      } catch (sessionError) {
        console.warn('获取会话信息失败:', sessionError);
      }

      return basicState;
    }));

    // 保存到全局标签页状态
    await chrome.storage.local.set({
      globalTabsState: enhancedTabsState,
      lastSaveTime: Date.now(),
      workspaceId: currentWorkspaceId
    });

    console.log('已保存增强的标签页状态:', enhancedTabsState.length, '个标签页');
  } catch (error) {
    console.error('保存标签页状态失败:', error);
  }
}

// 关闭所有非固定标签页（保留用于兼容性）
async function closeAllNonPinnedTabs() {
  try {
    const allTabs = await chrome.tabs.query({ currentWindow: true, pinned: false });

    for (const tab of allTabs) {
      if (tab.id && tab.url !== 'chrome://newtab/') {
        await chrome.tabs.remove(tab.id);
      }
    }

    console.log('已关闭所有非固定标签页');
  } catch (error) {
    console.error('关闭标签页失败:', error);
  }
}

// 智能暂停非固定标签页（修复版本，解决标签页重复问题）
async function suspendAllNonPinnedTabs() {
  try {
    console.log('😴 开始智能暂停非固定标签页...');

    const allTabs = await chrome.tabs.query({
      currentWindow: true,
      pinned: false
    });

    console.log(`📋 找到 ${allTabs.length} 个非固定标签页`);

    const recentlyActiveThreshold = 5 * 60 * 1000; // 5分钟
    const currentTime = Date.now();

    let suspendedCount = 0;
    let skippedCount = 0;

    for (const tab of allTabs) {
      // 跳过新标签页和当前活动标签页
      if (!tab.id || tab.url === 'chrome://newtab/' || tab.active) {
        continue;
      }

      // 跳过Chrome内部页面
      if (tab.url && (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://'))) {
        continue;
      }

      // 智能暂停逻辑：跳过最近活跃的标签页
      const shouldSkipSuspension = await shouldSkipTabSuspension(tab, currentTime, recentlyActiveThreshold);

      if (shouldSkipSuspension) {
        console.log(`⏭️ 跳过暂停重要标签页: ${tab.title}`);
        skippedCount++;
        continue;
      }

      // 如果标签页还没有被暂停，则暂停它
      if (!tab.discarded) {
        try {
          await chrome.tabs.discard(tab.id);
          suspendedCount++;
          console.log(`✅ 标签页 ${tab.id} (${tab.title}) 已智能暂停`);
        } catch (error) {
          console.warn(`❌ 暂停标签页 ${tab.id} 失败:`, error);
          // 如果暂停失败，记录但不关闭标签页
        }
      } else {
        console.log(`⏭️ 标签页 ${tab.id} (${tab.title}) 已经被暂停`);
        skippedCount++;
      }
    }

    console.log(`✅ 智能标签页暂停完成: 暂停 ${suspendedCount} 个，跳过 ${skippedCount} 个`);
  } catch (error) {
    console.error('❌ 智能暂停标签页失败:', error);
  }
}

// 判断是否应该跳过标签页暂停
async function shouldSkipTabSuspension(tab: chrome.tabs.Tab, currentTime: number, threshold: number): Promise<boolean> {
  try {
    // 检查标签页是否包含表单数据（简单启发式）
    if (tab.url && (tab.url.includes('form') || tab.url.includes('edit') || tab.url.includes('compose'))) {
      return true;
    }

    // 检查是否是重要网站（可以根据需要扩展）
    const importantDomains = ['gmail.com', 'docs.google.com', 'github.com', 'stackoverflow.com'];
    if (tab.url && importantDomains.some(domain => tab.url!.includes(domain))) {
      return true;
    }

    // 其他情况允许暂停
    return false;
  } catch (error) {
    console.warn('检查标签页暂停条件失败:', error);
    return false; // 默认允许暂停
  }
}

// 保存所有标签页到存储
async function saveAllTabsToStorage() {
  try {
    const allTabs = await chrome.tabs.query({ currentWindow: true });
    const tabsData = allTabs.map(tab => ({
      id: tab.id,
      url: tab.url,
      title: tab.title,
      favIconUrl: tab.favIconUrl,
      pinned: tab.pinned,
      groupId: tab.groupId,
      index: tab.index
    }));

    await chrome.storage.local.set({
      [`allTabs_${Date.now()}`]: tabsData
    });

    console.log('已保存', tabsData.length, '个标签页到存储');
  } catch (error) {
    console.error('保存标签页失败:', error);
  }
}

// 隐藏不属于工作区的标签页
async function hideNonWorkspaceTabs(workspaceId: string) {
  try {
    const workspace = workspaces.get(workspaceId);
    if (!workspace || !workspace.groups) {
      return;
    }

    // 获取工作区中的所有URL
    const workspaceUrls = new Set<string>();
    workspace.groups.forEach(group => {
      group.tabs.forEach(tab => {
        if (tab.url) workspaceUrls.add(tab.url);
      });
    });

    // 获取当前所有标签页
    const allTabs = await chrome.tabs.query({ currentWindow: true });

    // 关闭不属于工作区的标签页（保留固定标签页和新标签页）
    for (const tab of allTabs) {
      if (tab.pinned) continue; // 保留固定标签页
      if (!tab.url || tab.url === 'chrome://newtab/') continue; // 保留新标签页

      if (!workspaceUrls.has(tab.url) && tab.id) {
        await chrome.tabs.remove(tab.id);
      }
    }

    console.log('已隐藏不属于工作区的标签页');
  } catch (error) {
    console.error('隐藏标签页失败:', error);
  }
}

// 应用工作区特定的固定设置（增强版本）
async function applyWorkspacePinning(workspace: Workspace) {
  try {
    console.log(`📌 [DEBUG-PIN] ========== 开始应用工作区固定设置 ==========`);
    console.log(`📌 [DEBUG-PIN] 工作区名称: ${workspace.name}`);
    console.log(`📌 [DEBUG-PIN] 工作区ID: ${workspace.id}`);

    const pinnedUrls = definePinnedUrlsForWorkspace(workspace.id);
    console.log(`📌 [DEBUG-PIN] 固定URL模式:`, pinnedUrls);

    const allTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📌 [DEBUG-PIN] 当前窗口有 ${allTabs.length} 个标签页`);

    // 收集需要固定和取消固定的标签页
    const tabsToPin: chrome.tabs.Tab[] = [];
    const tabsToUnpin: chrome.tabs.Tab[] = [];
    const pinningResults: Array<{tab: chrome.tabs.Tab, shouldBePinned: boolean, currentlyPinned: boolean}> = [];

    console.log(`📌 [DEBUG-PIN] ========== 分析每个标签页的固定状态 ==========`);
    for (const tab of allTabs) {
      if (!tab.id || !tab.url) {
        console.log(`⚠️ [DEBUG-PIN] 跳过无效标签页: ${tab.title} (ID: ${tab.id}, URL: ${tab.url})`);
        continue;
      }

      console.log(`📌 [DEBUG-PIN] 分析标签页: ${tab.title}`);
      console.log(`📌 [DEBUG-PIN]   - URL: ${tab.url}`);
      console.log(`📌 [DEBUG-PIN]   - 当前固定状态: ${tab.pinned ? '已固定' : '未固定'}`);

      const shouldBePinned = pinnedUrls.some(pattern => {
        const matches = tab.url!.includes(pattern);
        console.log(`📌 [DEBUG-PIN]   - 检查模式 "${pattern}": ${matches ? '匹配' : '不匹配'}`);
        return matches;
      });

      const currentlyPinned = tab.pinned || false;

      console.log(`📌 [DEBUG-PIN]   - 应该固定: ${shouldBePinned}`);
      console.log(`📌 [DEBUG-PIN]   - 当前固定: ${currentlyPinned}`);
      console.log(`📌 [DEBUG-PIN]   - 需要操作: ${shouldBePinned !== currentlyPinned ? '是' : '否'}`);

      pinningResults.push({
        tab,
        shouldBePinned,
        currentlyPinned
      });

      // 收集需要更改状态的标签页
      if (currentlyPinned !== shouldBePinned) {
        if (shouldBePinned) {
          tabsToPin.push(tab);
          console.log(`📌 [DEBUG-PIN] ✅ 需要固定: ${tab.title}`);
        } else {
          tabsToUnpin.push(tab);
          console.log(`📌 [DEBUG-PIN] ❌ 需要取消固定: ${tab.title}`);
        }
      } else {
        console.log(`📌 [DEBUG-PIN] ✅ 固定状态正确: ${tab.title} (${currentlyPinned ? '已固定' : '未固定'})`);
      }
    }

    // 批量执行固定操作
    const pinningOperations = await performBatchPinning(tabsToPin, tabsToUnpin);

    // 验证固定操作的完整性
    const verificationResult = await verifyPinningCompleteness(workspace.id, pinningResults);

    console.log(`✅ 工作区 ${workspace.id} 固定设置应用完成:`, {
      totalTabs: allTabs.length,
      pinned: pinningOperations.pinned,
      unpinned: pinningOperations.unpinned,
      failed: pinningOperations.failed,
      verificationPassed: verificationResult.passed
    });

    if (!verificationResult.passed) {
      console.warn('固定操作验证失败，尝试修复:', verificationResult.issues);
      await fixPinningIssues(verificationResult.issues);
    }

  } catch (error) {
    console.error('应用工作区固定设置失败:', error);
  }
}

// 批量执行固定操作
async function performBatchPinning(tabsToPin: chrome.tabs.Tab[], tabsToUnpin: chrome.tabs.Tab[]) {
  const results = {
    pinned: 0,
    unpinned: 0,
    failed: 0
  };

  // 批量固定标签页
  for (const tab of tabsToPin) {
    try {
      if (tab.id) {
        await chrome.tabs.update(tab.id, { pinned: true });
        results.pinned++;
        console.log(`✅ 标签页 ${tab.title} 已固定`);
      }
    } catch (error) {
      console.error(`固定标签页失败: ${tab.title}`, error);
      results.failed++;
    }
  }

  // 批量取消固定标签页
  for (const tab of tabsToUnpin) {
    try {
      if (tab.id) {
        await chrome.tabs.update(tab.id, { pinned: false });
        results.unpinned++;
        console.log(`✅ 标签页 ${tab.title} 已取消固定`);
      }
    } catch (error) {
      console.error(`取消固定标签页失败: ${tab.title}`, error);
      results.failed++;
    }
  }

  return results;
}

// 验证固定操作的完整性
async function verifyPinningCompleteness(workspaceId: string, expectedResults: Array<{tab: chrome.tabs.Tab, shouldBePinned: boolean, currentlyPinned: boolean}>) {
  try {
    // 等待一小段时间让固定操作生效
    await new Promise(resolve => setTimeout(resolve, 500));

    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const issues: Array<{tabId: number, title: string, expected: boolean, actual: boolean}> = [];

    for (const expected of expectedResults) {
      const currentTab = currentTabs.find(tab => tab.id === expected.tab.id);
      if (currentTab) {
        const actualPinned = currentTab.pinned || false;
        if (actualPinned !== expected.shouldBePinned) {
          issues.push({
            tabId: currentTab.id!,
            title: currentTab.title || '',
            expected: expected.shouldBePinned,
            actual: actualPinned
          });
        }
      }
    }

    return {
      passed: issues.length === 0,
      issues
    };
  } catch (error) {
    console.error('验证固定操作完整性失败:', error);
    return { passed: false, issues: [] };
  }
}

// 修复固定问题
async function fixPinningIssues(issues: Array<{tabId: number, title: string, expected: boolean, actual: boolean}>) {
  console.log(`尝试修复 ${issues.length} 个固定问题...`);

  for (const issue of issues) {
    try {
      await chrome.tabs.update(issue.tabId, { pinned: issue.expected });
      console.log(`🔧 修复标签页 ${issue.title}: ${issue.expected ? '固定' : '取消固定'}`);
    } catch (error) {
      console.error(`修复标签页固定状态失败: ${issue.title}`, error);
    }
  }
}

// ========== 新的统一标签页恢复架构 ==========

/**
 * 统一标签页恢复系统 - 唯一的标签页恢复入口
 * 替代原有的 restoreWorkspaceTabs 和 restoreWorkspaceFromConfig 函数
 */
async function unifiedTabRestoration(workspace: Workspace): Promise<{
  success: boolean;
  restoredCount: number;
  skippedCount: number;
  errors: Array<{ url: string; error: string }>;
}> {
  const startTime = Date.now();
  const result = {
    success: true,
    restoredCount: 0,
    skippedCount: 0,
    errors: [] as Array<{ url: string; error: string }>
  };

  try {
    console.log(`🔄 [UNIFIED-RESTORE] ========== 开始统一标签页恢复 ==========`);
    console.log(`🔄 [UNIFIED-RESTORE] 工作区: ${workspace.name} (${workspace.id})`);
    console.log(`🔄 [UNIFIED-RESTORE] 开始时间: ${new Date().toISOString()}`);

    // 步骤1: 构建标签页注册表（智能去重系统）
    console.log('📋 [UNIFIED-RESTORE] 步骤1: 构建标签页注册表...');
    const tabRegistry = await buildTabRegistry();
    console.log(`📋 [UNIFIED-RESTORE] 注册表构建完成，包含 ${tabRegistry.size} 个URL`);

    // 步骤2: 获取需要恢复的标签页列表
    console.log('📝 [UNIFIED-RESTORE] 步骤2: 获取恢复目标列表...');
    const targetTabs = await getWorkspaceTargetTabs(workspace);
    console.log(`📝 [UNIFIED-RESTORE] 找到 ${targetTabs.length} 个目标标签页`);

    if (targetTabs.length === 0) {
      console.log('📝 [UNIFIED-RESTORE] 没有需要恢复的标签页');
      return result;
    }

    // 步骤3: 智能去重检查和标签页创建
    console.log('🔍 [UNIFIED-RESTORE] 步骤3: 智能去重检查和创建...');
    for (const targetTab of targetTabs) {
      try {
        const shouldCreate = await shouldCreateTab(targetTab.url, tabRegistry);

        if (!shouldCreate.create) {
          console.log(`⏭️ [UNIFIED-RESTORE] 跳过重复标签页: ${targetTab.title} (${shouldCreate.reason})`);
          result.skippedCount++;
          continue;
        }

        // 创建新标签页
        const newTab = await chrome.tabs.create({
          url: targetTab.url,
          active: false,
          pinned: targetTab.pinned || false
        });

        if (newTab.id) {
          result.restoredCount++;
          console.log(`✅ [UNIFIED-RESTORE] 创建标签页: ${targetTab.title} (${targetTab.pinned ? '固定' : '普通'})`);

          // 更新注册表以防止后续重复创建
          tabRegistry.set(targetTab.url, {
            id: newTab.id,
            url: targetTab.url,
            title: targetTab.title,
            pinned: targetTab.pinned || false,
            createdBy: 'unified-restore'
          });
        }

        // 添加小延迟以避免浏览器过载
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`❌ [UNIFIED-RESTORE] 创建标签页失败: ${targetTab.url}`, error);
        result.errors.push({
          url: targetTab.url,
          error: (error as Error).message
        });
        result.success = false;
      }
    }

    const duration = Date.now() - startTime;
    console.log(`🎉 [UNIFIED-RESTORE] ========== 恢复完成 ==========`);
    console.log(`🎉 [UNIFIED-RESTORE] 耗时: ${duration}ms`);
    console.log(`🎉 [UNIFIED-RESTORE] 成功: ${result.restoredCount}, 跳过: ${result.skippedCount}, 错误: ${result.errors.length}`);

    return result;

  } catch (error) {
    console.error('❌ [UNIFIED-RESTORE] 统一恢复失败:', error);
    result.success = false;
    result.errors.push({
      url: 'system',
      error: (error as Error).message
    });
    return result;
  }
}

/**
 * 构建智能标签页注册表 - 支持多种URL匹配策略
 */
async function buildTabRegistry(): Promise<Map<string, {
  id: number;
  url: string;
  title: string;
  pinned: boolean;
  createdBy: string;
  matchStrategies: string[];
}>> {
  const registry = new Map();

  try {
    // 获取当前窗口的所有标签页
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [TAB-REGISTRY] 扫描到 ${currentTabs.length} 个现有标签页`);

    for (const tab of currentTabs) {
      if (tab.url && tab.id) {
        const tabInfo = {
          id: tab.id,
          url: tab.url,
          title: tab.title || '',
          pinned: tab.pinned || false,
          createdBy: 'existing',
          matchStrategies: [] as string[]
        };

        // 生成多种匹配键值
        const matchKeys = generateMatchKeys(tab.url);

        for (const key of matchKeys) {
          registry.set(key, { ...tabInfo, matchStrategies: [...tabInfo.matchStrategies, key] });
        }

        console.log(`📝 [TAB-REGISTRY] 注册: ${tab.title}`);
        console.log(`📝 [TAB-REGISTRY]   URL: ${tab.url}`);
        console.log(`📝 [TAB-REGISTRY]   匹配键: ${matchKeys.join(', ')}`);
      }
    }

    console.log(`📋 [TAB-REGISTRY] 注册表构建完成，包含 ${registry.size} 个条目`);
    return registry;

  } catch (error) {
    console.error('❌ [TAB-REGISTRY] 构建注册表失败:', error);
    return registry;
  }
}

/**
 * 为URL生成多种匹配键值
 */
function generateMatchKeys(url: string): string[] {
  const keys: string[] = [];

  try {
    const urlObj = new URL(url);

    // 1. 精确URL匹配
    keys.push(`exact:${url}`);

    // 2. 标准化URL匹配
    const normalizedUrl = smartNormalizeUrl(url);
    if (normalizedUrl !== url) {
      keys.push(`normalized:${normalizedUrl}`);
    }

    // 3. 域名匹配
    keys.push(`domain:${urlObj.hostname}`);

    // 4. 根域名匹配（处理子域名）
    const rootDomain = extractRootDomain(urlObj.hostname);
    if (rootDomain !== urlObj.hostname) {
      keys.push(`root-domain:${rootDomain}`);
    }

    // 5. 路径前缀匹配（多层级）
    const pathSegments = urlObj.pathname.split('/').filter(s => s.length > 0);
    for (let i = 1; i <= Math.min(pathSegments.length, 3); i++) {
      const pathPrefix = pathSegments.slice(0, i).join('/');
      if (pathPrefix) {
        keys.push(`path-prefix:${urlObj.hostname}/${pathPrefix}`);
      }
    }

    // 6. 特殊应用匹配（针对特定平台）
    const specialKey = generateSpecialMatchKey(url);
    if (specialKey) {
      keys.push(specialKey);
    }

    return keys;

  } catch (error) {
    console.warn(`⚠️ [MATCH-KEYS] 生成匹配键失败: ${url}`, error);
    return [`exact:${url}`]; // 至少返回精确匹配
  }
}

/**
 * 提取根域名（处理子域名）
 */
function extractRootDomain(hostname: string): string {
  const parts = hostname.split('.');
  if (parts.length >= 2) {
    return parts.slice(-2).join('.');
  }
  return hostname;
}

/**
 * 生成特殊应用的匹配键（针对特定平台的智能匹配）
 */
function generateSpecialMatchKey(url: string): string | null {
  try {
    const urlObj = new URL(url);

    // ChatGPT 相关
    if (urlObj.hostname.includes('openai.com')) {
      return 'app:chatgpt';
    }

    // Gemini 相关
    if (urlObj.hostname.includes('gemini.google.com')) {
      return 'app:gemini';
    }

    // 飞书相关 - 特殊处理复杂的子域名和路径
    if (urlObj.hostname.includes('feishu.cn')) {
      if (urlObj.pathname.includes('/drive/')) {
        return 'app:feishu-drive';
      }
      return 'app:feishu';
    }

    // 语雀相关
    if (urlObj.hostname.includes('yuque.com')) {
      return 'app:yuque';
    }

    // GitHub 相关
    if (urlObj.hostname.includes('github.com')) {
      return 'app:github';
    }

    // LobeHub 相关
    if (urlObj.hostname.includes('lobehub.com')) {
      return 'app:lobehub';
    }

    return null;
  } catch {
    return null;
  }
}

/**
 * 智能URL标准化 - 保留重要信息的同时进行标准化
 */
function smartNormalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);

    // 移除fragment但保留重要的查询参数和路径
    urlObj.hash = '';

    // 对于特定域名，保留重要的路径信息
    if (urlObj.hostname.includes('feishu.cn') ||
        urlObj.hostname.includes('yuque.com') ||
        urlObj.hostname.includes('notion.so')) {
      // 保留完整路径，只移除查询参数中的临时token
      const params = new URLSearchParams(urlObj.search);
      params.delete('token');
      params.delete('timestamp');
      params.delete('_t');
      urlObj.search = params.toString();
    }

    // 确保URL以/结尾的一致性
    if (urlObj.pathname.endsWith('/')) {
      urlObj.pathname = urlObj.pathname;
    } else if (!urlObj.pathname.includes('.')) {
      // 如果不是文件路径，添加/
      urlObj.pathname += '/';
    }

    return urlObj.toString();
  } catch (error) {
    console.warn(`⚠️ [URL-NORMALIZE] URL标准化失败: ${url}`, error);
    return url;
  }
}

/**
 * 提取域名
 */
function extractDomain(url: string): string {
  try {
    return new URL(url).hostname;
  } catch {
    return '';
  }
}

/**
 * 提取路径前缀（用于路径匹配）
 */
function extractPathPrefix(url: string, maxSegments: number = 2): string {
  try {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(segment => segment.length > 0);
    const prefix = pathSegments.slice(0, maxSegments).join('/');
    return `${urlObj.hostname}/${prefix}`;
  } catch {
    return '';
  }
}

/**
 * 获取工作区需要恢复的目标标签页列表
 */
async function getWorkspaceTargetTabs(workspace: Workspace): Promise<Array<{
  url: string;
  title: string;
  pinned: boolean;
  source: 'predefined' | 'saved';
}>> {
  const targetTabs: Array<{
    url: string;
    title: string;
    pinned: boolean;
    source: 'predefined' | 'saved';
  }> = [];

  try {
    // 1. 获取预定义的固定URL（优先级最高）
    const pinnedUrls = definePinnedUrlsForWorkspace(workspace.id);
    console.log(`📌 [TARGET-TABS] 预定义固定URL: ${pinnedUrls.length} 个`);

    for (const urlPattern of pinnedUrls) {
      // 根据工作区ID获取具体的URL和标题
      const predefinedTab = getPredefinedTabInfo(workspace.id, urlPattern);
      if (predefinedTab) {
        targetTabs.push({
          url: predefinedTab.url,
          title: predefinedTab.title,
          pinned: true,
          source: 'predefined'
        });
      }
    }

    // 2. 获取工作区配置中保存的标签页
    if (workspace.groups && workspace.groups.length > 0) {
      console.log(`💾 [TARGET-TABS] 工作区保存的分组: ${workspace.groups.length} 个`);

      for (const group of workspace.groups) {
        if (group.tabs && group.tabs.length > 0) {
          for (const tab of group.tabs) {
            if (tab.url) {
              // 检查是否已经在预定义列表中
              const isDuplicate = targetTabs.some(existing =>
                smartNormalizeUrl(existing.url) === smartNormalizeUrl(tab.url)
              );

              if (!isDuplicate) {
                targetTabs.push({
                  url: tab.url,
                  title: tab.title,
                  pinned: tab.pinned || false,
                  source: 'saved'
                });
              }
            }
          }
        }
      }
    }

    console.log(`📝 [TARGET-TABS] 总计目标标签页: ${targetTabs.length} 个`);
    targetTabs.forEach((tab, index) => {
      console.log(`  ${index + 1}. ${tab.title} (${tab.source}, ${tab.pinned ? '固定' : '普通'})`);
    });

    return targetTabs;

  } catch (error) {
    console.error('❌ [TARGET-TABS] 获取目标标签页失败:', error);
    return targetTabs;
  }
}

/**
 * 智能去重检查 - 决定是否应该创建新标签页
 */
async function shouldCreateTab(url: string, registry: Map<string, any>): Promise<{
  create: boolean;
  reason: string;
  matchedKey?: string;
}> {
  try {
    console.log(`🔍 [SHOULD-CREATE] 检查URL: ${url}`);

    // 生成目标URL的所有匹配键
    const targetKeys = generateMatchKeys(url);
    console.log(`🔍 [SHOULD-CREATE] 生成匹配键: ${targetKeys.join(', ')}`);

    // 按优先级检查匹配
    const priorityOrder = [
      'exact:',      // 精确匹配 - 最高优先级
      'normalized:', // 标准化匹配
      'app:',        // 应用特定匹配
      'path-prefix:', // 路径前缀匹配
      'domain:',     // 域名匹配
      'root-domain:' // 根域名匹配 - 最低优先级
    ];

    for (const prefix of priorityOrder) {
      for (const key of targetKeys) {
        if (key.startsWith(prefix) && registry.has(key)) {
          const existingTab = registry.get(key);
          console.log(`❌ [SHOULD-CREATE] 找到匹配: ${key} -> ${existingTab.title}`);

          // 特殊处理：对于路径前缀匹配，需要更精确的验证
          if (prefix === 'path-prefix:') {
            const shouldSkip = await validatePathPrefixMatch(url, existingTab.url);
            if (!shouldSkip) {
              continue; // 继续检查其他匹配
            }
          }

          return {
            create: false,
            reason: getMatchReasonDescription(prefix),
            matchedKey: key
          };
        }
      }
    }

    console.log(`✅ [SHOULD-CREATE] 无重复，可以创建`);
    return { create: true, reason: '无重复，可以创建' };

  } catch (error) {
    console.error('❌ [SHOULD-CREATE] 去重检查失败:', error);
    return { create: true, reason: '检查失败，默认创建' };
  }
}

/**
 * 验证路径前缀匹配的准确性
 */
async function validatePathPrefixMatch(newUrl: string, existingUrl: string): Promise<boolean> {
  try {
    const newUrlObj = new URL(newUrl);
    const existingUrlObj = new URL(existingUrl);

    // 对于飞书等复杂应用，检查具体的功能路径
    if (newUrlObj.hostname.includes('feishu.cn')) {
      // 如果都是 /drive/ 路径，认为是重复
      if (newUrlObj.pathname.includes('/drive/') && existingUrlObj.pathname.includes('/drive/')) {
        return true;
      }
      // 如果路径差异较大，不认为是重复
      const newSegments = newUrlObj.pathname.split('/').filter(s => s.length > 0);
      const existingSegments = existingUrlObj.pathname.split('/').filter(s => s.length > 0);

      if (newSegments.length > 2 && existingSegments.length > 2) {
        // 比较前3个路径段
        const newPrefix = newSegments.slice(0, 3).join('/');
        const existingPrefix = existingSegments.slice(0, 3).join('/');
        return newPrefix === existingPrefix;
      }
    }

    return true; // 默认认为是重复
  } catch {
    return true;
  }
}

/**
 * 获取匹配原因的描述
 */
function getMatchReasonDescription(prefix: string): string {
  const descriptions: Record<string, string> = {
    'exact:': '精确URL匹配',
    'normalized:': '标准化URL匹配',
    'app:': '应用特定匹配',
    'path-prefix:': '路径前缀匹配',
    'domain:': '域名匹配',
    'root-domain:': '根域名匹配'
  };

  return descriptions[prefix] || '未知匹配类型';
}

/**
 * 获取预定义标签页的详细信息
 */
function getPredefinedTabInfo(workspaceId: string, urlPattern: string): {
  url: string;
  title: string;
} | null {
  // 预定义工作区的标签页配置
  const predefinedTabs: Record<string, Record<string, { url: string; title: string }>> = {
    'ai-main': {
      'chat.openai.com': { url: 'https://chat.openai.com/', title: 'ChatGPT' },
      'gemini.google.com': { url: 'https://gemini.google.com/', title: 'Gemini' },
      'chat-preview.lobehub.com': { url: 'https://chat-preview.lobehub.com/discover', title: 'LobeHub' },
      'www.perplexity.ai': { url: 'https://www.perplexity.ai/', title: 'Perplexity' },
      'grok.x.ai': { url: 'https://grok.x.ai/', title: 'Grok' },
      'aistudio.google.com': { url: 'https://aistudio.google.com/', title: 'AI Studio' }
    },
    'ai-tools': {
      'deepask.cc': { url: 'https://deepask.cc/', title: 'DeepAsk' },
      'gptfun.cc': { url: 'https://gptfun.cc/', title: 'GPTFun' },
      'demo.fuclaude.com': { url: 'https://demo.fuclaude.com/', title: 'Claude' },
      'dify.ai': { url: 'https://dify.ai/', title: 'Dify' }
    },
    'daily-work': {
      'www.yuque.com': { url: 'https://www.yuque.com/', title: '语雀' },
      'feishu.cn': { url: 'https://p1b9rnchwd.feishu.cn/drive/home/', title: '飞书' }
    },
    'tech-forum': {
      'linux.do': { url: 'https://linux.do/', title: 'Linux.do' },
      'www.nodeseek.com': { url: 'https://www.nodeseek.com/', title: 'NodeSeek' },
      'www.nodeloc.cc': { url: 'https://www.nodeloc.cc/', title: 'NodeLoc' },
      'meta.appinn.net': { url: 'https://meta.appinn.net/', title: 'Appinn' },
      'follow.is': { url: 'https://follow.is/', title: 'Follow' }
    }
  };

  const workspaceTabs = predefinedTabs[workspaceId];
  if (!workspaceTabs) {
    return null;
  }

  // 尝试通过URL模式匹配找到对应的标签页信息
  for (const [pattern, tabInfo] of Object.entries(workspaceTabs)) {
    if (urlPattern.includes(pattern) || pattern.includes(urlPattern)) {
      return tabInfo;
    }
  }

  return null;
}

// ========== 旧的恢复函数已删除，使用新的 unifiedTabRestoration ==========

// 应用工作区标签页分组（已合并到unifiedTabRestoration中）
// 此函数已废弃，功能合并到applyWorkspaceTabVisibility中

// 为工作区组织当前标签页
async function organizeCurrentTabsForWorkspace(workspaceId: string) {
  try {
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const groups = await performSimpleAIGrouping(currentTabs);

    // AI分组功能已改为基于固定状态
    await applyAIPinning(groups);

    // 保存到工作区
    const workspace = workspaces.get(workspaceId);
    if (workspace) {
      workspace.groups = await organizeTabsIntoGroups(currentTabs);
      workspaces.set(workspaceId, workspace);
      await saveWorkspacesToStorage();
    }
  } catch (error) {
    console.error('组织工作区标签页失败:', error);
  }
}

// 将标签页组织成分组结构
async function organizeTabsIntoGroups(tabs: chrome.tabs.Tab[]): Promise<TabGroup[]> {
  const groupMap = new Map<number, TabGroup>();

  for (const tab of tabs) {
    const groupId = tab.groupId || -1;

    if (!groupMap.has(groupId)) {
      let groupName = '未分组';
      let groupColor = 'grey';

      if (groupId !== -1) {
        try {
          const group = await chrome.tabGroups.get(groupId);
          groupName = group.title || `分组 ${groupId}`;
          groupColor = group.color;
        } catch (error) {
          console.warn('获取分组信息失败:', groupId);
        }
      }

      groupMap.set(groupId, {
        id: groupId.toString(),
        name: groupName,
        color: groupColor,
        tabs: []
      });
    }

    const group = groupMap.get(groupId)!;
    group.tabs.push({
      id: tab.id || 0,
      title: tab.title || '',
      url: tab.url || '',
      favIconUrl: tab.favIconUrl,
      groupId: tab.groupId
    });
  }

  return Array.from(groupMap.values());
}

// 生成工作区ID
function generateWorkspaceId(): string {
  return 'workspace_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// ========== 旧的 normalizeUrl 函数已删除，使用新的 smartNormalizeUrl ==========

// 定义每个工作区的固定URL模式
function definePinnedUrlsForWorkspace(workspaceId: string): string[] {
  const pinnedUrlPatterns: { [key: string]: string[] } = {
    'ai-main': [
      'chat.openai.com',
      'gemini.google.com',
      'lobehub.com',
      'perplexity.ai',
      'grok.x.ai',
      'aistudio.google.com'
    ],
    'ai-secondary': [
      'deepask.cc',
      'fun4ai.khthink.cn',
      'clivia.fun',
      'aabao.eu.cc',
      'haomo.de',
      'fuclaude.com'
    ],
    'ai-tools': [
      'dify.ai',
      'promptpilot.volcengine.com'
    ],
    'tech-forum': [
      'linux.do',
      'nodeloc.cc',
      'nodeseek.com',
      'appinn.net',
      'follow.is'
    ],
    'daily-work': [
      'yuque.com',
      'feishu.cn'
    ]
  };

  return pinnedUrlPatterns[workspaceId] || [];
}

// 简单的AI分组逻辑
async function performSimpleAIGrouping(tabs: chrome.tabs.Tab[]) {
  const groups = new Map<string, { name: string; color: string; tabs: chrome.tabs.Tab[] }>();

  tabs.forEach(tab => {
    if (!tab.url) return;

    try {
      const domain = new URL(tab.url).hostname;
      const category = categorizeWebsite(domain);

      if (!groups.has(category)) {
        groups.set(category, {
          name: category,
          color: getCategoryColor(category),
          tabs: []
        });
      }

      groups.get(category)!.tabs.push(tab);
    } catch (error) {
      console.error('分析标签页失败:', tab.url, error);
    }
  });

  return Array.from(groups.values()).filter(group => group.tabs.length > 1);
}

// 网站分类逻辑
function categorizeWebsite(domain: string): string {
  const categories: Record<string, string[]> = {
    '开发工具': ['github.com', 'stackoverflow.com', 'developer.mozilla.org', 'codepen.io'],
    'AI工具': ['openai.com', 'claude.ai', 'gemini.google.com', 'perplexity.ai'],
    '社交媒体': ['twitter.com', 'facebook.com', 'linkedin.com', 'instagram.com'],
    '视频平台': ['youtube.com', 'bilibili.com', 'netflix.com', 'twitch.tv'],
    '购物网站': ['amazon.com', 'taobao.com', 'jd.com', 'ebay.com'],
    '新闻资讯': ['news.google.com', 'bbc.com', 'cnn.com', 'reuters.com']
  };

  for (const [category, domains] of Object.entries(categories)) {
    if (domains.some((d: string) => domain.includes(d))) {
      return category;
    }
  }

  return '其他';
}

// 获取分类颜色
function getCategoryColor(category: string): chrome.tabGroups.ColorEnum {
  const colors: Record<string, chrome.tabGroups.ColorEnum> = {
    '开发工具': 'blue',
    'AI工具': 'purple',
    '社交媒体': 'pink',
    '视频平台': 'red',
    '购物网站': 'orange',
    '新闻资讯': 'green',
    '其他': 'grey'
  };

  return colors[category] || 'grey';
}

// AI智能固定功能（替代分组）
async function applyAIPinning(groups: { name: string; color: string; tabs: chrome.tabs.Tab[] }[]) {
  try {
    // 定义重要类别，这些类别的标签页将被固定
    const importantCategories = ['开发工具', 'AI工具', '生产力工具'];

    for (const group of groups) {
      const shouldPin = importantCategories.includes(group.name);

      for (const tab of group.tabs) {
        if (tab.id && tab.pinned !== shouldPin) {
          try {
            await chrome.tabs.update(tab.id, { pinned: shouldPin });
            console.log(`${shouldPin ? '固定' : '取消固定'}标签页: ${tab.title} (${group.name})`);
          } catch (error) {
            console.error(`更新标签页固定状态失败: ${tab.title}`, error);
          }
        }
      }
    }
  } catch (error) {
    console.error('应用AI智能固定失败:', error);
    throw error;
  }
}

// 内存管理和性能优化
async function optimizeMemoryUsage() {
  try {
    const tabs = await chrome.tabs.query({ currentWindow: true });
    const discardedCount = tabs.filter(tab => tab.discarded).length;
    const totalTabs = tabs.length;

    console.log(`内存优化状态: ${discardedCount}/${totalTabs} 标签页已暂停`);

    // 如果暂停的标签页比例过低，进行额外的内存优化
    if (totalTabs > 10 && discardedCount / totalTabs < 0.3) {
      await performAdditionalMemoryOptimization();
    }
  } catch (error) {
    console.error('内存优化检查失败:', error);
  }
}

// 执行额外的内存优化
async function performAdditionalMemoryOptimization() {
  try {
    const tabs = await chrome.tabs.query({
      currentWindow: true,
      pinned: false,
      active: false,
      discarded: false
    });

    // 按最后访问时间排序，优先暂停最久未访问的标签页
    const sortedTabs = tabs.sort((a, b) => {
      // 简单的启发式：根据index判断访问顺序
      return (b.index || 0) - (a.index || 0);
    });

    // 暂停最久未访问的标签页（最多暂停一半）
    const tabsToSuspend = sortedTabs.slice(0, Math.floor(sortedTabs.length / 2));

    for (const tab of tabsToSuspend) {
      if (tab.id) {
        try {
          await chrome.tabs.discard(tab.id);
          console.log(`额外内存优化: 暂停标签页 ${tab.title}`);
        } catch (error) {
          console.warn(`额外暂停失败: ${tab.title}`, error);
        }
      }
    }
  } catch (error) {
    console.error('执行额外内存优化失败:', error);
  }
}

// 定期执行内存优化（每5分钟）
setInterval(optimizeMemoryUsage, 5 * 60 * 1000);

// ========== 重复的函数定义已删除，使用新的统一恢复架构 ==========

console.log('工作区管理器后台脚本加载完成');